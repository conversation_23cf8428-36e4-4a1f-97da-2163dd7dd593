{"name": "industrial-property-scraper", "version": "1.0.0", "description": "Outil de scraping pour bâtiments industriels - Lyon et Bordeaux", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["scraping", "immobilier", "industriel", "lyon", "bordeaux"], "author": "Esteban R.", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "dependencies": {"jsdom": "^26.1.0"}}