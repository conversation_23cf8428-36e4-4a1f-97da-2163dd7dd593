// SCRAPER AVEC VRAIES URLs - Recherche les vraies annonces disponibles
const http = require('http');
const https = require('https');
const { URL } = require('url');

const PORT = 3000;

// Critères de recherche selon ton cahier des charges
const SEARCH_CRITERIA = {
  minPrice: 800000,
  maxPrice: 900000,
  minBuildingArea: 400,
  idealBuildingArea: 1100,
  minLandArea: 3500,
  cities: ['Lyon', 'Bordeaux'],
  maxDistanceToHighway: 15,
  minCeilingHeight: 5.5,
  idealCeilingHeight: 6.5
};

// Base de données en mémoire pour stocker les annonces scrapées
let realProperties = [];
let scrapingStatus = { isRunning: false, currentStep: '', progress: 0 };

// Fonction pour faire des requêtes HTTP avec headers réalistes
function fetchPage(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https') ? https : http;

    const options = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'fr-FR,fr;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none'
      }
    };

    client.get(url, options, (res) => {
      let data = '';

      // Gérer la compression gzip
      if (res.headers['content-encoding'] === 'gzip') {
        const zlib = require('zlib');
        const gunzip = zlib.createGunzip();
        res.pipe(gunzip);
        gunzip.on('data', chunk => data += chunk);
        gunzip.on('end', () => resolve(data));
      } else {
        res.on('data', chunk => data += chunk);
        res.on('end', () => resolve(data));
      }
    }).on('error', reject);
  });
}

// Fonction pour extraire les vraies URLs d'annonces depuis une page de résultats
function extractPropertyUrls(html, baseUrl) {
  const urls = [];

  // Patterns pour trouver les liens d'annonces
  const patterns = [
    /href="([^"]*\/annonce[^"]*)/gi,
    /href="([^"]*\/vente[^"]*)/gi,
    /href="([^"]*\/detail[^"]*)/gi,
    /href="([^"]*\/bien[^"]*)/gi
  ];

  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(html)) !== null) {
      let url = match[1];

      // Construire l'URL complète
      if (url.startsWith('/')) {
        url = baseUrl + url;
      } else if (!url.startsWith('http')) {
        url = baseUrl + '/' + url;
      }

      // Vérifier que c'est bien une annonce
      if (url.includes('annonce') || url.includes('vente') || url.includes('detail')) {
        if (!urls.includes(url)) {
          urls.push(url);
        }
      }
    }
  }

  return urls;
}

// Fonction pour extraire les données d'une annonce
function extractPropertyData(html, sourceUrl) {
  // Fonction utilitaire pour extraire du texte entre balises
  const extractText = (pattern) => {
    const match = html.match(pattern);
    return match ? match[1].trim() : '';
  };

  // Extraire le titre
  const title = extractText(/<title[^>]*>([^<]+)<\/title>/i) ||
                extractText(/<h1[^>]*>([^<]+)<\/h1>/i) ||
                'Titre non trouvé';

  // Extraire le prix
  const pricePatterns = [
    /(\d+(?:\s?\d+)*)\s*€/,
    /prix[^>]*>([^<]*\d+[^<]*)</i,
    /montant[^>]*>([^<]*\d+[^<]*)</i
  ];

  let price = 0;
  for (const pattern of pricePatterns) {
    const match = html.match(pattern);
    if (match) {
      const priceStr = match[1].replace(/\s/g, '');
      const priceNum = parseInt(priceStr);
      if (priceNum > 100000) { // Prix réaliste
        price = priceNum;
        break;
      }
    }
  }

  // Extraire la superficie
  const areaPatterns = [
    /(\d+(?:[,.]?\d+)?)\s*m[²2]/i,
    /surface[^>]*>([^<]*\d+[^<]*m)/i,
    /superficie[^>]*>([^<]*\d+[^<]*m)/i
  ];

  let buildingArea = 0;
  for (const pattern of areaPatterns) {
    const match = html.match(pattern);
    if (match) {
      const areaStr = match[1].replace(',', '.');
      const areaNum = parseFloat(areaStr);
      if (areaNum > 100) { // Superficie réaliste
        buildingArea = areaNum;
        break;
      }
    }
  }

  // Extraire la description
  const descPatterns = [
    /<meta[^>]*name="description"[^>]*content="([^"]+)"/i,
    /<div[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)</i,
    /<p[^>]*class="[^"]*description[^"]*"[^>]*>([^<]+)</i
  ];

  let description = '';
  for (const pattern of descPatterns) {
    const match = html.match(pattern);
    if (match) {
      description = match[1].trim();
      break;
    }
  }

  return {
    title: title.replace(/[<>]/g, '').substring(0, 100),
    description: description.replace(/[<>]/g, '').substring(0, 300),
    price,
    buildingArea,
    sourceUrl
  };
}

// Fonction pour vérifier si une annonce est disponible
async function checkPropertyAvailability(url) {
  try {
    const html = await fetchPage(url);

    // Vérifier les indicateurs d'indisponibilité
    const unavailableKeywords = [
      'vendu', 'sold', 'retiré', 'indisponible', 'plus disponible',
      'hors marché', 'suspendu', 'réservé', 'en cours de vente',
      '404', 'page non trouvée', 'erreur'
    ];

    const htmlLower = html.toLowerCase();
    const isUnavailable = unavailableKeywords.some(keyword => htmlLower.includes(keyword));

    return !isUnavailable && html.length > 1000; // Page doit avoir du contenu
  } catch (error) {
    console.log(`⚠️ Erreur vérification ${url}: ${error.message}`);
    return false;
  }
}

// Scraper pour Geolocaux avec vraies URLs
async function scrapeGeolocauxReal() {
  console.log('🔍 Scraping Geolocaux avec vraies URLs...');
  scrapingStatus.currentStep = 'Connexion à Geolocaux...';

  const properties = [];
  const baseUrl = 'https://www.geolocaux.com';

  try {
    // URLs de recherche réelles pour Geolocaux
    const searchUrls = [
      'https://www.geolocaux.com/recherche?type=vente&bien=entrepot&ville=lyon',
      'https://www.geolocaux.com/recherche?type=vente&bien=entrepot&ville=bordeaux'
    ];

    for (const searchUrl of searchUrls) {
      try {
        scrapingStatus.currentStep = `Recherche sur ${searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux'}...`;

        const html = await fetchPage(searchUrl);
        const propertyUrls = extractPropertyUrls(html, baseUrl);

        console.log(`📋 ${propertyUrls.length} URLs trouvées sur Geolocaux`);

        // Traiter les premières URLs trouvées
        const urlsToProcess = propertyUrls.slice(0, 5);

        for (let i = 0; i < urlsToProcess.length; i++) {
          const url = urlsToProcess[i];
          scrapingStatus.currentStep = `Vérification annonce ${i + 1}/${urlsToProcess.length}...`;
          scrapingStatus.progress = (i / urlsToProcess.length) * 50;

          try {
            // Vérifier la disponibilité
            const isAvailable = await checkPropertyAvailability(url);
            if (!isAvailable) {
              console.log(`❌ Annonce indisponible: ${url}`);
              continue;
            }

            // Extraire les données
            const html = await fetchPage(url);
            const propertyData = extractPropertyData(html, url);

            // Vérifier si l'annonce correspond aux critères
            if (propertyData.price >= SEARCH_CRITERIA.minPrice &&
                propertyData.price <= SEARCH_CRITERIA.maxPrice &&
                propertyData.buildingArea >= SEARCH_CRITERIA.minBuildingArea) {

              const property = {
                id: `geolocaux-real-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: propertyData.title,
                description: propertyData.description,
                price: propertyData.price,
                city: searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux',
                address: `${propertyData.city || (searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux')}`,
                buildingArea: propertyData.buildingArea,
                landArea: Math.floor(propertyData.buildingArea * 3.5), // Estimation
                source: 'geolocaux',
                sourceUrl: url,
                publishedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
                scrapedAt: new Date(),
                isNew: true,
                isAvailable: true,
                matchScore: calculateMatchScore({
                  price: propertyData.price,
                  buildingArea: propertyData.buildingArea,
                  city: searchUrl.includes('lyon') ? 'Lyon' : 'Bordeaux'
                })
              };

              properties.push(property);
              console.log(`✅ Vraie annonce ajoutée: ${property.title} - ${property.price}€`);
            }

          } catch (error) {
            console.log(`❌ Erreur traitement ${url}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        console.log(`❌ Erreur recherche Geolocaux: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ Erreur générale Geolocaux: ${error.message}`);
  }

  return properties;
}

// Scraper pour SeLoger avec vraies URLs
async function scrapeSeLogerReal() {
  console.log('🔍 Scraping SeLoger avec vraies URLs...');
  scrapingStatus.currentStep = 'Connexion à SeLoger...';

  const properties = [];
  const baseUrl = 'https://www.seloger.com';

  try {
    // URLs de recherche réelles pour SeLoger
    const searchUrls = [
      'https://www.seloger.com/list.htm?types=11&places=[{%22inseeCodes%22:[69123]}]&price=800000/900000',
      'https://www.seloger.com/list.htm?types=11&places=[{%22inseeCodes%22:[33063]}]&price=800000/900000'
    ];

    for (const searchUrl of searchUrls) {
      try {
        scrapingStatus.currentStep = `Recherche sur ${searchUrl.includes('69123') ? 'Lyon' : 'Bordeaux'}...`;

        const html = await fetchPage(searchUrl);
        const propertyUrls = extractPropertyUrls(html, baseUrl);

        console.log(`📋 ${propertyUrls.length} URLs trouvées sur SeLoger`);

        // Traiter les premières URLs trouvées
        const urlsToProcess = propertyUrls.slice(0, 3);

        for (let i = 0; i < urlsToProcess.length; i++) {
          const url = urlsToProcess[i];
          scrapingStatus.currentStep = `Vérification annonce ${i + 1}/${urlsToProcess.length}...`;
          scrapingStatus.progress = 50 + (i / urlsToProcess.length) * 50;

          try {
            // Vérifier la disponibilité
            const isAvailable = await checkPropertyAvailability(url);
            if (!isAvailable) {
              console.log(`❌ Annonce indisponible: ${url}`);
              continue;
            }

            // Extraire les données
            const html = await fetchPage(url);
            const propertyData = extractPropertyData(html, url);

            // Vérifier si l'annonce correspond aux critères
            if (propertyData.price >= SEARCH_CRITERIA.minPrice &&
                propertyData.price <= SEARCH_CRITERIA.maxPrice &&
                propertyData.buildingArea >= SEARCH_CRITERIA.minBuildingArea) {

              const property = {
                id: `seloger-real-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                title: propertyData.title,
                description: propertyData.description,
                price: propertyData.price,
                city: searchUrl.includes('69123') ? 'Lyon' : 'Bordeaux',
                address: `${propertyData.city || (searchUrl.includes('69123') ? 'Lyon' : 'Bordeaux')}`,
                buildingArea: propertyData.buildingArea,
                landArea: Math.floor(propertyData.buildingArea * 3.5), // Estimation
                source: 'seloger',
                sourceUrl: url,
                publishedAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
                scrapedAt: new Date(),
                isNew: true,
                isAvailable: true,
                matchScore: calculateMatchScore({
                  price: propertyData.price,
                  buildingArea: propertyData.buildingArea,
                  city: searchUrl.includes('69123') ? 'Lyon' : 'Bordeaux'
                })
              };

              properties.push(property);
              console.log(`✅ Vraie annonce ajoutée: ${property.title} - ${property.price}€`);
            }

          } catch (error) {
            console.log(`❌ Erreur traitement ${url}: ${error.message}`);
          }

          // Pause entre les requêtes
          await new Promise(resolve => setTimeout(resolve, 2000));
        }

      } catch (error) {
        console.log(`❌ Erreur recherche SeLoger: ${error.message}`);
      }
    }

  } catch (error) {
    console.log(`❌ Erreur générale SeLoger: ${error.message}`);
  }

  return properties;
}

// Fonction pour calculer le score de correspondance
function calculateMatchScore(property) {
  let score = 0;

  // Prix (30 points)
  if (property.price >= SEARCH_CRITERIA.minPrice && property.price <= SEARCH_CRITERIA.maxPrice) {
    score += 30;
  } else if (property.price <= SEARCH_CRITERIA.maxPrice * 1.1) {
    score += 20;
  }

  // Superficie bâtiment (25 points)
  if (property.buildingArea >= SEARCH_CRITERIA.minBuildingArea) {
    score += 25;
    if (property.buildingArea >= SEARCH_CRITERIA.idealBuildingArea) {
      score += 5; // Bonus
    }
  }

  // Ville (20 points)
  if (SEARCH_CRITERIA.cities.some(city => property.city.toLowerCase().includes(city.toLowerCase()))) {
    score += 20;
  }

  // Bonus pour caractéristiques (25 points)
  const desc = property.description ? property.description.toLowerCase() : '';
  if (desc.includes('poids lourd') || desc.includes('camion')) score += 5;
  if (desc.includes('quai') || desc.includes('chargement')) score += 5;
  if (desc.includes('bureau') || desc.includes('office')) score += 5;
  if (desc.includes('stockage') || desc.includes('entrepot')) score += 5;
  if (desc.includes('industriel') || desc.includes('logistique')) score += 5;

  return Math.min(score, 100);
}

// Fonction principale de scraping avec vraies URLs
async function runRealUrlScraping() {
  if (scrapingStatus.isRunning) {
    console.log('⚠️ Scraping déjà en cours...');
    return { success: false, message: 'Scraping déjà en cours' };
  }

  scrapingStatus.isRunning = true;
  scrapingStatus.currentStep = 'Initialisation...';
  scrapingStatus.progress = 0;

  console.log('🚀 Démarrage du scraping avec vraies URLs...');
  realProperties = []; // Reset

  try {
    // Scraper les deux sources
    const [geolocauxProps, selogerProps] = await Promise.all([
      scrapeGeolocauxReal(),
      scrapeSeLogerReal()
    ]);

    // Combiner et trier par score
    realProperties = [...geolocauxProps, ...selogerProps]
      .filter(prop => prop.matchScore >= 70) // Seuil minimum
      .sort((a, b) => b.matchScore - a.matchScore);

    scrapingStatus.currentStep = 'Terminé !';
    scrapingStatus.progress = 100;

    console.log(`✅ Scraping avec vraies URLs terminé: ${realProperties.length} propriétés trouvées et vérifiées`);

    return {
      success: true,
      propertiesFound: realProperties.length,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('❌ Erreur scraping avec vraies URLs:', error);
    return {
      success: false,
      error: error.message,
      timestamp: new Date()
    };
  } finally {
    scrapingStatus.isRunning = false;
    setTimeout(() => {
      scrapingStatus.progress = 0;
    }, 2000);
  }
}

// Fonction utilitaire pour le temps écoulé
function getTimeAgo(date) {
  const diffMs = Date.now() - date;
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

  if (diffMins < 60) {
    return `il y a ${diffMins} min`;
  } else {
    return `il y a ${diffHours}h`;
  }
}

// Interface web simplifiée pour tester
function generateRealUrlHTML() {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Industrial Property Scraper - Vraies URLs</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc; color: #334155; line-height: 1.6; padding: 20px;
        }
        .container { max-width: 1000px; margin: 0 auto; }
        .header {
            background: linear-gradient(135deg, #7c3aed, #a855f7);
            color: white; padding: 30px; text-align: center; margin-bottom: 30px; border-radius: 12px;
        }
        .btn {
            padding: 15px 30px; border: none; border-radius: 8px; font-weight: 600;
            cursor: pointer; transition: all 0.2s; margin: 10px; font-size: 1.1rem;
            background: #22c55e; color: white;
        }
        .btn:hover { background: #16a34a; }
        .btn:disabled { background: #94a3b8; cursor: not-allowed; }
        .status { margin: 20px 0; padding: 15px; background: white; border-radius: 8px; text-align: center; }
        .properties { margin-top: 30px; }
        .property-card {
            background: white; border-radius: 12px; padding: 24px; margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-left: 4px solid #7c3aed;
        }
        .property-title { font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 10px; }
        .property-url {
            background: #f8fafc; padding: 8px 12px; border-radius: 6px;
            font-family: monospace; font-size: 0.8rem; color: #64748b;
            word-break: break-all; margin: 10px 0;
        }
        .btn-link {
            background: #7c3aed; color: white; text-decoration: none;
            padding: 10px 20px; border-radius: 6px; display: inline-block; margin-top: 10px;
        }
        .btn-link:hover { background: #6d28d9; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Industrial Property Scraper</h1>
            <p>Test du scraping avec vraies URLs</p>
        </div>

        <div class="status">
            <h3>🔗 Scraper avec vraies URLs</h3>
            <p>L'agent va chercher les vraies URLs d'annonces sur Geolocaux et SeLoger</p>
            <button onclick="startRealScraping()" class="btn" id="scrapBtn">
                🚀 Lancer le scraping des vraies URLs
            </button>
            <div id="status" style="margin-top: 15px;"></div>
        </div>

        <div class="properties" id="properties">
            ${realProperties.map(property => `
                <div class="property-card">
                    <div class="property-title">${property.title}</div>
                    <div>Prix: ${property.price.toLocaleString('fr-FR')} €</div>
                    <div>Superficie: ${property.buildingArea}m²</div>
                    <div>Score: ${property.matchScore}%</div>
                    <div class="property-url">URL: ${property.sourceUrl}</div>
                    <a href="${property.sourceUrl}" target="_blank" class="btn-link">
                        🔗 Voir l'annonce réelle
                    </a>
                </div>
            `).join('')}
        </div>

        ${realProperties.length === 0 ? `
            <div style="text-align: center; padding: 40px; background: white; border-radius: 12px;">
                <h3>🔍 Aucune propriété trouvée</h3>
                <p>Lancez le scraping pour rechercher les vraies annonces</p>
            </div>
        ` : ''}
    </div>

    <script>
        async function startRealScraping() {
            const btn = document.getElementById('scrapBtn');
            const status = document.getElementById('status');

            btn.disabled = true;
            btn.textContent = '⏳ Scraping en cours...';
            status.innerHTML = '🔄 Recherche des vraies URLs...';

            try {
                const response = await fetch('/api/scraping/real-urls', { method: 'POST' });
                const result = await response.json();

                if (result.success) {
                    status.innerHTML = '✅ Scraping terminé - Rechargement...';
                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    status.innerHTML = '❌ Erreur: ' + (result.error || result.message);
                    btn.disabled = false;
                    btn.textContent = '🚀 Relancer le scraping';
                }
            } catch (error) {
                status.innerHTML = '❌ Erreur de connexion';
                btn.disabled = false;
                btn.textContent = '🚀 Relancer le scraping';
            }
        }
    </script>
</body>
</html>
  `;
}

// Serveur HTTP
const server = http.createServer(async (req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(generateRealUrlHTML());
  } else if (url.pathname === '/api/scraping/real-urls' && req.method === 'POST') {
    const result = await runRealUrlScraping();
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify(result));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

// Démarrage
server.listen(PORT, () => {
  console.log(`🚀 Agent de scraping VRAIES URLs démarré sur http://localhost:${PORT}`);
  console.log('🔗 Mode VRAIES URLs activé - Extraction des vraies annonces');
  console.log('✅ Prêt ! Ouvrez http://localhost:3000 pour scraper les vraies annonces');
});