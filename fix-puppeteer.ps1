# Script pour corriger Puppeteer sur Windows
Write-Host "🔧 Correction de Puppeteer pour Windows" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green

# Vérifier si Chrome est installé
Write-Host "`n📦 Vérification de Chrome..." -ForegroundColor Yellow
$chromePaths = @(
    "${env:ProgramFiles}\Google\Chrome\Application\chrome.exe",
    "${env:ProgramFiles(x86)}\Google\Chrome\Application\chrome.exe",
    "${env:LOCALAPPDATA}\Google\Chrome\Application\chrome.exe"
)

$chromeFound = $false
foreach ($path in $chromePaths) {
    if (Test-Path $path) {
        Write-Host "✅ Chrome trouvé: $path" -ForegroundColor Green
        $chromeFound = $true
        $chromePath = $path
        break
    }
}

if (-not $chromeFound) {
    Write-Host "❌ Chrome non trouvé. Installation de Chrome..." -ForegroundColor Red
    Write-Host "Veuillez installer Chrome depuis https://www.google.com/chrome/" -ForegroundColor Yellow
    Write-Host "Puis relancez ce script." -ForegroundColor Yellow
    exit 1
}

# Désinstaller Puppeteer existant
Write-Host "`n🗑️ Nettoyage de l'installation Puppeteer..." -ForegroundColor Yellow
npm uninstall puppeteer

# Supprimer le cache Puppeteer
$puppeteerCache = "$env:USERPROFILE\.cache\puppeteer"
if (Test-Path $puppeteerCache) {
    Write-Host "🧹 Suppression du cache Puppeteer..." -ForegroundColor Cyan
    Remove-Item -Recurse -Force $puppeteerCache
}

# Réinstaller Puppeteer avec Chrome existant
Write-Host "`n📥 Installation de Puppeteer (utilisation Chrome existant)..." -ForegroundColor Yellow
$env:PUPPETEER_SKIP_CHROMIUM_DOWNLOAD = "true"
npm install puppeteer

# Créer un fichier de configuration
Write-Host "`n⚙️ Configuration de Puppeteer..." -ForegroundColor Yellow
$configContent = @"
// Configuration Puppeteer pour Windows
const puppeteer = require('puppeteer');

const launchOptions = {
    executablePath: '$($chromePath.Replace('\', '\\'))',
    headless: true,
    args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
    ]
};

module.exports = { puppeteer, launchOptions };
"@

$configContent | Out-File -FilePath "puppeteer-config.js" -Encoding UTF8

# Test de Puppeteer
Write-Host "`n🧪 Test de Puppeteer..." -ForegroundColor Yellow
$testScript = @"
const { puppeteer, launchOptions } = require('./puppeteer-config.js');

(async () => {
    try {
        console.log('🚀 Test de lancement de Puppeteer...');
        const browser = await puppeteer.launch(launchOptions);
        console.log('✅ Puppeteer lancé avec succès !');
        
        const page = await browser.newPage();
        await page.goto('https://www.google.com');
        console.log('✅ Navigation réussie !');
        
        await browser.close();
        console.log('✅ Test terminé avec succès !');
        console.log('🎉 Puppeteer est maintenant fonctionnel !');
    } catch (error) {
        console.error('❌ Erreur lors du test:', error.message);
        process.exit(1);
    }
})();
"@

$testScript | Out-File -FilePath "test-puppeteer.js" -Encoding UTF8

Write-Host "`n🧪 Exécution du test..." -ForegroundColor Cyan
node test-puppeteer.js

if ($LASTEXITCODE -eq 0) {
    Write-Host "`n🎉 Puppeteer configuré avec succès !" -ForegroundColor Green
    Write-Host "✅ Vous pouvez maintenant utiliser le scraper LIVE" -ForegroundColor Green
    Write-Host "`n🚀 Pour lancer le scraper LIVE:" -ForegroundColor Cyan
    Write-Host "   node live-scraper-fixed.js" -ForegroundColor White
    
    # Créer la version corrigée du scraper
    Write-Host "`n📝 Création du scraper LIVE corrigé..." -ForegroundColor Yellow
    
    # Copier le scraper et modifier la configuration
    $liveScraperContent = Get-Content "live-scraper.js" -Raw
    $fixedContent = $liveScraperContent -replace "const puppeteer = require\('puppeteer'\);", "const { puppeteer, launchOptions } = require('./puppeteer-config.js');"
    $fixedContent = $fixedContent -replace "browser = await puppeteer\.launch\(\{[^}]+\}\);", "browser = await puppeteer.launch(launchOptions);"
    
    $fixedContent | Out-File -FilePath "live-scraper-fixed.js" -Encoding UTF8
    
    Write-Host "✅ Scraper LIVE corrigé créé: live-scraper-fixed.js" -ForegroundColor Green
    
} else {
    Write-Host "`n❌ Échec de la configuration de Puppeteer" -ForegroundColor Red
    Write-Host "🔧 Solutions possibles:" -ForegroundColor Yellow
    Write-Host "   1. Redémarrer en tant qu'administrateur" -ForegroundColor White
    Write-Host "   2. Désactiver temporairement l'antivirus" -ForegroundColor White
    Write-Host "   3. Utiliser la version fonctionnelle: node working-scraper.js" -ForegroundColor White
}

# Nettoyage
Remove-Item "test-puppeteer.js" -ErrorAction SilentlyContinue

Write-Host "`n📋 Résumé:" -ForegroundColor Cyan
Write-Host "   - Version fonctionnelle: working-scraper.js (ACTIVE)" -ForegroundColor White
Write-Host "   - Version LIVE: live-scraper-fixed.js (si Puppeteer OK)" -ForegroundColor White
Write-Host "   - Configuration: puppeteer-config.js" -ForegroundColor White
