var mapperTab=function(){"use strict";var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var a=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};a.prototype=t.prototype}else a={};return Object.defineProperty(a,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(a,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})})),a}var a={},r={},n={},s={};var o=Object.freeze({__proto__:null,default:function(e){return{all:e=e||new Map,on:function(t,a){var r=e.get(t);r?r.push(a):e.set(t,[a])},off:function(t,a){var r=e.get(t);r&&(a?r.splice(r.indexOf(a)>>>0,1):e.set(t,[]))},emit:function(t,a){var r=e.get(t);r&&r.slice().map((function(e){e(a)})),(r=e.get("*"))&&r.slice().map((function(e){e(t,a)}))}}}}),i=t(o),c=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(s,"__esModule",{value:!0}),s.EventEmitter=void 0;const d=c(i);s.EventEmitter=class{#e=(0,d.default)();on(e,t){return this.#e.on(e,t),this}once(e,t){const a=r=>{t(r),this.off(e,a)};return this.on(e,a)}off(e,t){return this.#e.off(e,t),this}emit(e,t){this.#e.emit(e,t)}removeAllListeners(e){return e?this.#e.all.delete(e):this.#e.all.clear(),this}};var u,l={};Object.defineProperty(l,"__esModule",{value:!0}),l.LogType=void 0,function(e){e.bidi="bidi",e.cdp="cdp",e.debug="debug",e.debugError="debug:error",e.debugInfo="debug:info"}(u||(l.LogType=u={}));var h={};Object.defineProperty(h,"__esModule",{value:!0}),h.ProcessingQueue=void 0;const p=l;class m{static LOGGER_PREFIX=`${p.LogType.debug}:queue`;#t;#a;#r=[];#n=!1;constructor(e,t){this.#a=e,this.#t=t}add(e,t){this.#r.push([e,t]),this.#s()}async#s(){if(!this.#n){for(this.#n=!0;this.#r.length>0;){const e=this.#r.shift();if(!e)continue;const[t,a]=e;this.#t?.(m.LOGGER_PREFIX,"Processing event:",a),await t.then((e=>{if("error"!==e.kind)return this.#a(e.value);this.#t?.(p.LogType.debugError,"Event threw before sending:",e.error.message,e.error.stack)})).catch((e=>{this.#t?.(p.LogType.debugError,"Event was not processed:",e?.message)}))}this.#n=!1}}}h.ProcessingQueue=m;var f={},g={},y={};Object.defineProperty(y,"__esModule",{value:!0});var S,v,w,C,b,x={};Object.defineProperty(x,"__esModule",{value:!0}),x.EVENT_NAMES=x.Network=x.BrowsingContext=x.Log=x.Script=x.BiDiModule=void 0,function(e){e.Browser="browser",e.BrowsingContext="browsingContext",e.Cdp="cdp",e.Input="input",e.Log="log",e.Network="network",e.Script="script",e.Session="session"}(S||(x.BiDiModule=S={})),function(e){var t;(t=e.EventNames||(e.EventNames={})).Message="script.message",t.RealmCreated="script.realmCreated",t.RealmDestroyed="script.realmDestroyed"}(v||(x.Script=v={})),function(e){(e.EventNames||(e.EventNames={})).LogEntryAdded="log.entryAdded"}(w||(x.Log=w={})),function(e){var t;(t=e.EventNames||(e.EventNames={})).ContextCreated="browsingContext.contextCreated",t.ContextDestroyed="browsingContext.contextDestroyed",t.DomContentLoaded="browsingContext.domContentLoaded",t.DownloadWillBegin="browsingContext.downloadWillBegin",t.FragmentNavigated="browsingContext.fragmentNavigated",t.Load="browsingContext.load",t.NavigationAborted="browsingContext.navigationAborted",t.NavigationFailed="browsingContext.navigationFailed",t.NavigationStarted="browsingContext.navigationStarted",t.UserPromptClosed="browsingContext.userPromptClosed",t.UserPromptOpened="browsingContext.userPromptOpened"}(C||(x.BrowsingContext=C={})),function(e){var t;(t=e.EventNames||(e.EventNames={})).AuthRequired="network.authRequired",t.BeforeRequestSent="network.beforeRequestSent",t.FetchError="network.fetchError",t.ResponseCompleted="network.responseCompleted",t.ResponseStarted="network.responseStarted"}(b||(x.Network=b={})),x.EVENT_NAMES=new Set([...Object.values(S),...Object.values(C.EventNames),...Object.values(w.EventNames),...Object.values(b.EventNames),...Object.values(v.EventNames)]);var P={};Object.defineProperty(P,"__esModule",{value:!0});var I={};Object.defineProperty(I,"__esModule",{value:!0}),I.UnderspecifiedStoragePartitionException=I.UnableToSetFileInputException=I.UnableToSetCookieException=I.NoSuchStoragePartitionException=I.UnsupportedOperationException=I.UnableToCloseBrowserException=I.UnableToCaptureScreenException=I.UnknownErrorException=I.UnknownCommandException=I.SessionNotCreatedException=I.NoSuchUserContextException=I.NoSuchScriptException=I.NoSuchRequestException=I.NoSuchNodeException=I.NoSuchInterceptException=I.NoSuchHistoryEntryException=I.NoSuchHandleException=I.NoSuchFrameException=I.NoSuchElementException=I.NoSuchAlertException=I.MoveTargetOutOfBoundsException=I.InvalidSessionIdException=I.InvalidArgumentException=I.Exception=void 0;class k{error;message;stacktrace;constructor(e,t,a){this.error=e,this.message=t,this.stacktrace=a}toErrorResponse(e){return{type:"error",id:e,error:this.error,message:this.message,stacktrace:this.stacktrace}}}I.Exception=k;I.InvalidArgumentException=class extends k{constructor(e,t){super("invalid argument",e,t)}};I.InvalidSessionIdException=class extends k{constructor(e,t){super("invalid session id",e,t)}};I.MoveTargetOutOfBoundsException=class extends k{constructor(e,t){super("move target out of bounds",e,t)}};I.NoSuchAlertException=class extends k{constructor(e,t){super("no such alert",e,t)}};I.NoSuchElementException=class extends k{constructor(e,t){super("no such element",e,t)}};I.NoSuchFrameException=class extends k{constructor(e,t){super("no such frame",e,t)}};I.NoSuchHandleException=class extends k{constructor(e,t){super("no such handle",e,t)}};I.NoSuchHistoryEntryException=class extends k{constructor(e,t){super("no such history entry",e,t)}};I.NoSuchInterceptException=class extends k{constructor(e,t){super("no such intercept",e,t)}};I.NoSuchNodeException=class extends k{constructor(e,t){super("no such node",e,t)}};I.NoSuchRequestException=class extends k{constructor(e,t){super("no such request",e,t)}};I.NoSuchScriptException=class extends k{constructor(e,t){super("no such script",e,t)}};I.NoSuchUserContextException=class extends k{constructor(e,t){super("no such user context",e,t)}};I.SessionNotCreatedException=class extends k{constructor(e,t){super("session not created",e,t)}};I.UnknownCommandException=class extends k{constructor(e,t){super("unknown command",e,t)}};I.UnknownErrorException=class extends k{constructor(e,t=(new Error).stack){super("unknown error",e,t)}};I.UnableToCaptureScreenException=class extends k{constructor(e,t){super("unable to capture screen",e,t)}};I.UnableToCloseBrowserException=class extends k{constructor(e,t){super("unable to close browser",e,t)}};I.UnsupportedOperationException=class extends k{constructor(e,t){super("unsupported operation",e,t)}};I.NoSuchStoragePartitionException=class extends k{constructor(e,t){super("no such storage partition",e,t)}};I.UnableToSetCookieException=class extends k{constructor(e,t){super("unable to set cookie",e,t)}};I.UnableToSetFileInputException=class extends k{constructor(e,t){super("unable to set file input",e,t)}};I.UnderspecifiedStoragePartitionException=class extends k{constructor(e,t){super("underspecified storage partition",e,t)}};var R={};Object.defineProperty(R,"__esModule",{value:!0}),function(t){var a=e&&e.__createBinding||(Object.create?function(e,t,a,r){void 0===r&&(r=a);var n=Object.getOwnPropertyDescriptor(t,a);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,r,n)}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),n=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&a(t,e,n);return r(t,e),t},s=e&&e.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.ChromiumBidi=t.Cdp=void 0,t.Cdp=n(y),t.ChromiumBidi=n(x),s(P,t),s(I,t),s(R,t)}(g);var E={};Object.defineProperty(E,"__esModule",{value:!0}),E.BidiNoOpParser=void 0;E.BidiNoOpParser=class{parseActivateParams(e){return e}parseCaptureScreenshotParams(e){return e}parseCloseParams(e){return e}parseCreateParams(e){return e}parseGetTreeParams(e){return e}parseHandleUserPromptParams(e){return e}parseNavigateParams(e){return e}parsePrintParams(e){return e}parseReloadParams(e){return e}parseSetViewportParams(e){return e}parseTraverseHistoryParams(e){return e}parseGetSessionParams(e){return e}parseSendCommandParams(e){return e}parseAddPreloadScriptParams(e){return e}parseCallFunctionParams(e){return e}parseDisownParams(e){return e}parseEvaluateParams(e){return e}parseGetRealmsParams(e){return e}parseRemovePreloadScriptParams(e){return e}parsePerformActionsParams(e){return e}parseReleaseActionsParams(e){return e}parseSetFilesParams(e){return e}parseAddInterceptParams(e){return e}parseContinueRequestParams(e){return e}parseContinueResponseParams(e){return e}parseContinueWithAuthParams(e){return e}parseFailRequestParams(e){return e}parseProvideResponseParams(e){return e}parseRemoveInterceptParams(e){return e}parseSetPermissionsParams(e){return e}parseSubscribeParams(e){return e}parseDeleteCookiesParams(e){return e}parseGetCookiesParams(e){return e}parseSetCookieParams(e){return e}};var _={};Object.defineProperty(_,"__esModule",{value:!0}),_.BrowserProcessor=void 0;const T=g;_.BrowserProcessor=class{#o;constructor(e){this.#o=e}close(){return setTimeout((()=>this.#o.sendCommand("Browser.close")),0),{}}async createUserContext(){return{userContext:(await this.#o.sendCommand("Target.createBrowserContext")).browserContextId}}async removeUserContext(e){if("default"===e)throw new T.InvalidArgumentException("`default` user context cannot be removed");try{await this.#o.sendCommand("Target.disposeBrowserContext",{browserContextId:e})}catch(e){if(e.message.startsWith("Failed to find context with id"))throw new T.NoSuchUserContextException(e.message);throw e}return{}}async getUserContexts(){return{userContexts:[{userContext:"default"},...(await this.#o.sendCommand("Target.getBrowserContexts")).browserContextIds.map((e=>({userContext:e})))]}}};var N={};Object.defineProperty(N,"__esModule",{value:!0}),N.CdpProcessor=void 0;N.CdpProcessor=class{#i;#c;#o;constructor(e,t,a){this.#i=e,this.#c=t,this.#o=a}getSession(e){const t=e.context,a=this.#i.getContext(t).cdpTarget.cdpSessionId;return void 0===a?{}:{session:a}}async sendCommand(e){const t=e.session?this.#c.getCdpClient(e.session):this.#o;return{result:await t.sendCommand(e.method,e.params),session:e.session}}};var j={},O={},M={},A={};Object.defineProperty(A,"__esModule",{value:!0}),A.uuidv4=void 0,A.uuidv4=function(){if("crypto"in globalThis&&"randomUUID"in globalThis.crypto)return globalThis.crypto.randomUUID();const e=new Uint8Array(16);"crypto"in globalThis&&"getRandomValues"in globalThis.crypto?globalThis.crypto.getRandomValues(e):require("crypto").webcrypto.getRandomValues(e),e[6]=15&e[6]|64,e[8]=63&e[8]|128;const t=e=>e.reduce(((e,t)=>e+t.toString(16).padStart(2,"0")),"");return[t(e.subarray(0,4)),t(e.subarray(4,6)),t(e.subarray(6,8)),t(e.subarray(8,10)),t(e.subarray(10,16))].join("-")};var B={};Object.defineProperty(B,"__esModule",{value:!0}),B.ChannelProxy=void 0;const z=g,D=l,L=A;class Z{#d;#u=(0,L.uuidv4)();#t;constructor(e,t){this.#d=e,this.#t=t}async init(e,t){const a=await Z.#l(e),r=await Z.#h(e,a);return this.#p(e,a,t),r}async startListenerFromWindow(e,t){try{const a=await this.#m(e);this.#p(e,a,t)}catch(e){this.#t?.(D.LogType.debugError,e)}}static#f(){return`(${String((()=>{const e=[];let t=null;return{async getMessage(){const a=e.length>0?Promise.resolve():new Promise((e=>{t=e}));return await a,e.shift()},sendMessage(a){e.push(a),null!==t&&(t(),t=null)}}}))})()`}static async#l(e){const t=await e.cdpClient.sendCommand("Runtime.evaluate",{expression:this.#f(),contextId:e.executionContextId,serializationOptions:{serialization:"idOnly"}});if(t.exceptionDetails||void 0===t.result.objectId)throw new Error("Cannot create channel");return t.result.objectId}static async#h(e,t){return(await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>e.sendMessage)),arguments:[{objectId:t}],executionContextId:e.executionContextId,serializationOptions:{serialization:"idOnly"}})).result.objectId}async#p(e,t,a){for(;;)try{const r=await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((async e=>await e.getMessage())),arguments:[{objectId:t}],awaitPromise:!0,executionContextId:e.executionContextId,serializationOptions:{serialization:"deep",maxDepth:this.#d.serializationOptions?.maxObjectDepth??void 0}});if(r.exceptionDetails)throw r.exceptionDetails;for(const t of e.associatedBrowsingContexts)a.registerEvent({type:"event",method:z.ChromiumBidi.Script.EventNames.Message,params:{channel:this.#d.channel,data:e.cdpToBidiValue(r,this.#d.ownership??"none"),source:e.source}},t.id)}catch(e){this.#t?.(D.LogType.debugError,e);break}}async#m(e){const t=await e.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>{const t=window;if(void 0===t[e])return new Promise((a=>t[e]=a));const a=t[e];return delete t[e],a})),arguments:[{value:this.#u}],executionContextId:e.executionContextId,awaitPromise:!0,serializationOptions:{serialization:"idOnly"}});if(void 0!==t.exceptionDetails||void 0===t.result.objectId)throw new Error(`ChannelHandle not found in window["${this.#u}"]`);return t.result.objectId}getEvalInWindowStr(){const e=String(((e,t)=>{const a=window;return void 0===a[e]?a[e]=t:(a[e](t),delete a[e]),t.sendMessage})),t=Z.#f();return`(${e})('${this.#u}',${t})`}}B.ChannelProxy=Z,Object.defineProperty(M,"__esModule",{value:!0}),M.Realm=void 0;const U=g,F=l,q=A,V=B;class ${#g;#y;#S;#t;#v;#w;#C;constructor(e,t,a,r,n,s,o){this.#g=e,this.#y=t,this.#S=a,this.#t=r,this.#v=n,this.#w=s,this.#C=o,this.#C.addRealm(this)}cdpToBidiValue(e,t){const a=this.serializeForBiDi(e.result.deepSerializedValue,new Map);if(e.result.objectId){const r=e.result.objectId;"root"===t?(a.handle=r,this.#C.knownHandlesToRealmMap.set(r,this.realmId)):this.#b(r).catch((e=>this.#t?.(F.LogType.debugError,e)))}if("object"===e.result.type)switch(e.result.subtype){case"generator":case"iterator":a.type=e.result.subtype,delete a.value}return a}serializeForBiDi(e,t){if(Object.hasOwn(e,"weakLocalObjectReference")){const a=e.weakLocalObjectReference;t.has(a)||t.set(a,(0,q.uuidv4)()),e.internalId=t.get(a),delete e.weakLocalObjectReference}if("platformobject"===e.type)return{type:"object"};const a=e.value;if(void 0===a)return e;if(["array","set","htmlcollection","nodelist"].includes(e.type))for(const e in a)a[e]=this.serializeForBiDi(a[e],t);if(["object","map"].includes(e.type))for(const e in a)a[e]=[this.serializeForBiDi(a[e][0],t),this.serializeForBiDi(a[e][1],t)];return e}get realmId(){return this.#w}get executionContextId(){return this.#S}get origin(){return this.#v}get source(){return{realm:this.realmId}}get cdpClient(){return this.#g}get baseInfo(){return{realm:this.realmId,origin:this.origin}}async evaluate(e,t,a,r,n=!1){const s=await this.cdpClient.sendCommand("Runtime.evaluate",{contextId:this.executionContextId,expression:e,awaitPromise:t,serializationOptions:$.#x("deep",r),userGesture:n});return s.exceptionDetails?await this.#P(s.exceptionDetails,0,a):{realm:this.realmId,result:this.cdpToBidiValue(s,a),type:"success"}}initialize(){for(const e of this.associatedBrowsingContexts)this.#y.registerEvent({type:"event",method:U.ChromiumBidi.Script.EventNames.RealmCreated,params:this.realmInfo},e.id)}async serializeCdpObject(e,t){const a=$.#I(e),r=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>e)),awaitPromise:!1,arguments:[a],serializationOptions:{serialization:"deep"},executionContextId:this.executionContextId});return this.cdpToBidiValue(r,t)}static#I(e){return void 0!==e.objectId?{objectId:e.objectId}:void 0!==e.unserializableValue?{unserializableValue:e.unserializableValue}:{value:e.value}}async stringifyObject(e){const{result:t}=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String((e=>String(e))),awaitPromise:!1,arguments:[e],returnByValue:!0,executionContextId:this.executionContextId});return t.value}async#k(e){const t=[];for(const[a,r]of e){let e;e="string"==typeof a?{value:a}:await this.deserializeForCdp(a);const n=await this.deserializeForCdp(r);t.push(e),t.push(n)}return t}async#R(e){return await Promise.all(e.map((e=>this.deserializeForCdp(e))))}async#E(e,t,a){const r=e.stackTrace?.callFrames.map((e=>({url:e.url,functionName:e.functionName,lineNumber:e.lineNumber-t,columnNumber:e.columnNumber})))??[],n=e.exception;return{exception:await this.serializeCdpObject(n,a),columnNumber:e.columnNumber,lineNumber:e.lineNumber-t,stackTrace:{callFrames:r},text:await this.stringifyObject(n)||e.text}}async callFunction(e,t,a,r,n,s,o=!1){const i=`(...args) => {\n      function callFunction(f, args) {\n        const deserializedThis = args.shift();\n        const deserializedArgs = args;\n        return f.apply(deserializedThis, deserializedArgs);\n      }\n      return callFunction((\n        ${e}\n      ), args);\n    }`,c=[await this.deserializeForCdp(t),...await Promise.all(a.map((async e=>await this.deserializeForCdp(e))))];let d;try{d=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:i,awaitPromise:r,arguments:c,serializationOptions:$.#x("deep",s),executionContextId:this.executionContextId,userGesture:o})}catch(e){if(-32e3===e.code&&["Could not find object with given id","Argument should belong to the same JavaScript world as target object","Invalid remote object id"].includes(e.message))throw new U.NoSuchHandleException("Handle was not found.");throw e}return d.exceptionDetails?await this.#P(d.exceptionDetails,1,n):{type:"success",result:this.cdpToBidiValue(d,n),realm:this.realmId}}async deserializeForCdp(e){if("handle"in e&&e.handle)return{objectId:e.handle};if("handle"in e||"sharedId"in e)throw new U.NoSuchHandleException("Handle was not found.");switch(e.type){case"undefined":return{unserializableValue:"undefined"};case"null":return{unserializableValue:"null"};case"string":return{value:e.value};case"number":return"NaN"===e.value?{unserializableValue:"NaN"}:"-0"===e.value?{unserializableValue:"-0"}:"Infinity"===e.value?{unserializableValue:"Infinity"}:"-Infinity"===e.value?{unserializableValue:"-Infinity"}:{value:e.value};case"boolean":return{value:Boolean(e.value)};case"bigint":return{unserializableValue:`BigInt(${JSON.stringify(e.value)})`};case"date":return{unserializableValue:`new Date(Date.parse(${JSON.stringify(e.value)}))`};case"regexp":return{unserializableValue:`new RegExp(${JSON.stringify(e.value.pattern)}, ${JSON.stringify(e.value.flags)})`};case"map":{const t=await this.#k(e.value),{result:a}=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>{const t=new Map;for(let a=0;a<e.length;a+=2)t.set(e[a],e[a+1]);return t})),awaitPromise:!1,arguments:t,returnByValue:!1,executionContextId:this.executionContextId});return{objectId:a.objectId}}case"object":{const t=await this.#k(e.value),{result:a}=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>{const t={};for(let a=0;a<e.length;a+=2){t[e[a]]=e[a+1]}return t})),awaitPromise:!1,arguments:t,returnByValue:!1,executionContextId:this.executionContextId});return{objectId:a.objectId}}case"array":{const t=await this.#R(e.value),{result:a}=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>e)),awaitPromise:!1,arguments:t,returnByValue:!1,executionContextId:this.executionContextId});return{objectId:a.objectId}}case"set":{const t=await this.#R(e.value),{result:a}=await this.cdpClient.sendCommand("Runtime.callFunctionOn",{functionDeclaration:String(((...e)=>new Set(e))),awaitPromise:!1,arguments:t,returnByValue:!1,executionContextId:this.executionContextId});return{objectId:a.objectId}}case"channel":{const t=new V.ChannelProxy(e.value,this.#t);return{objectId:await t.init(this,this.#y)}}}throw new Error(`Value ${JSON.stringify(e)} is not deserializable.`)}async#P(e,t,a){return{exceptionDetails:await this.#E(e,t,a),realm:this.realmId,type:"exception"}}static#x(e,t){return{serialization:e,additionalParameters:$.#_(t),...$.#T(t)}}static#_(e){const t={};return void 0!==e.maxDomDepth&&(t.maxNodeDepth=null===e.maxDomDepth?1e3:e.maxDomDepth),void 0!==e.includeShadowTree&&(t.includeShadowTree=e.includeShadowTree),t}static#T(e){return void 0===e.maxObjectDepth||null===e.maxObjectDepth?{}:{maxDepth:e.maxObjectDepth}}async#b(e){try{await this.cdpClient.sendCommand("Runtime.releaseObject",{objectId:e})}catch(e){if(-32e3!==e.code||"Invalid remote object id"!==e.message)throw e}}async disown(e){this.#C.knownHandlesToRealmMap.get(e)===this.realmId&&(await this.#b(e),this.#C.knownHandlesToRealmMap.delete(e))}dispose(){for(const e of this.associatedBrowsingContexts)this.#y.registerEvent({type:"event",method:U.ChromiumBidi.Script.EventNames.RealmDestroyed,params:{realm:this.realmId}},e.id)}}M.Realm=$,Object.defineProperty(O,"__esModule",{value:!0}),O.DedicatedWorkerRealm=void 0;const K=M;class H extends K.Realm{#N;constructor(e,t,a,r,n,s,o,i){super(e,t,a,r,n,o,i),this.#N=s,this.initialize()}get associatedBrowsingContexts(){return this.#N.associatedBrowsingContexts}get realmType(){return"dedicated-worker"}get source(){return{realm:this.realmId,context:this.associatedBrowsingContexts[0]?.id}}get realmInfo(){return{...this.baseInfo,type:this.realmType,owners:[this.#N.realmId]}}}O.DedicatedWorkerRealm=H;var W={},J={};Object.defineProperty(J,"__esModule",{value:!0}),J.assert=void 0,J.assert=function(e,t){if(!e)throw new Error(t??"Internal assertion failed.")};var G={};Object.defineProperty(G,"__esModule",{value:!0}),G.Deferred=void 0;class X{#j=!1;#O;#M;#A;get isFinished(){return this.#j}constructor(){this.#O=new Promise(((e,t)=>{this.#M=e,this.#A=t})),this.#O.catch((e=>{}))}then(e,t){return this.#O.then(e,t)}catch(e){return this.#O.catch(e)}resolve(e){this.#j||(this.#j=!0,this.#M(e))}reject(e){this.#j||(this.#j=!0,this.#A(e))}finally(e){return this.#O.finally(e)}[Symbol.toStringTag]="Promise"}G.Deferred=X;var Y={};Object.defineProperty(Y,"__esModule",{value:!0}),Y.inchesFromCm=void 0,Y.inchesFromCm=function(e){return e/2.54};var Q={},ee={};Object.defineProperty(ee,"__esModule",{value:!0}),ee.parseSharedId=ee.getSharedId=void 0;const te="_element_";ee.getSharedId=function(e,t,a,r){return r?`f.${e}.d.${t}.e.${a}`:`${t}${te}${a}`},ee.parseSharedId=function(e){const t=function(e){const t=e.match(new RegExp(`(.*)${te}(.*)`));if(!t)return null;const a=t[1],r=t[2];if(void 0===a||void 0===r)return null;const n=parseInt(r??"");return isNaN(n)?null:{documentId:a,backendNodeId:n}}(e);if(null!==t)return{...t,frameId:void 0};const a=e.match(/f\.(.*)\.d\.(.*)\.e\.([0-9]*)/);if(!a)return null;const r=a[1],n=a[2],s=a[3];if(void 0===r||void 0===n||void 0===s)return null;const o=parseInt(s??"");return isNaN(o)?null:{frameId:r,documentId:n,backendNodeId:o}},Object.defineProperty(Q,"__esModule",{value:!0}),Q.WindowRealm=void 0;const ae=g,re=M,ne=ee;class se extends re.Realm{#B;#i;#z;sandbox;constructor(e,t,a,r,n,s,o,i,c,d,u){super(a,r,n,s,o,i,c),this.#B=e,this.#i=t,this.#z=u,this.sandbox=d,this.initialize()}#D(e){const t=this.#i.getAllContexts().find((t=>t.navigableId===e));return t?.id??"UNKNOWN"}get browsingContext(){return this.#i.getContext(this.#B)}get associatedBrowsingContexts(){return[this.browsingContext]}get realmType(){return"window"}get realmInfo(){return{...this.baseInfo,type:this.realmType,context:this.#B,sandbox:this.sandbox}}get source(){return{realm:this.realmId,context:this.browsingContext.id}}serializeForBiDi(e,t){const a=e.value;if("node"===e.type){if(Object.hasOwn(a,"backendNodeId")){let t=this.browsingContext.navigableId??"UNKNOWN";Object.hasOwn(a,"loaderId")&&(t=a.loaderId,delete a.loaderId),e.sharedId=(0,ne.getSharedId)(this.#D(t),t,a.backendNodeId,this.#z),delete a.backendNodeId}if(Object.hasOwn(a,"children"))for(const e in a.children)a.children[e]=this.serializeForBiDi(a.children[e],t);Object.hasOwn(a,"shadowRoot")&&null!==a.shadowRoot&&(a.shadowRoot=this.serializeForBiDi(a.shadowRoot,t)),""===a.namespaceURI&&(a.namespaceURI=null)}return super.serializeForBiDi(e,t)}async deserializeForCdp(e){if("sharedId"in e&&e.sharedId){const t=(0,ne.parseSharedId)(e.sharedId);if(null===t)throw new ae.NoSuchNodeException(`SharedId "${e.sharedId}" was not found.`);const{documentId:a,backendNodeId:r}=t;if(this.browsingContext.navigableId!==a)throw new ae.NoSuchNodeException(`SharedId "${e.sharedId}" belongs to different document. Current document is ${this.browsingContext.navigableId}.`);try{const{object:e}=await this.cdpClient.sendCommand("DOM.resolveNode",{backendNodeId:r,executionContextId:this.executionContextId});return{objectId:e.objectId}}catch(t){if(-32e3===t.code&&"No node with given id found"===t.message)throw new ae.NoSuchNodeException(`SharedId "${e.sharedId}" was not found.`);throw new ae.UnknownErrorException(t.message,t.stack)}}return await super.deserializeForCdp(e)}async evaluate(e,t,a,r,n){return await this.#i.getContext(this.#B).targetUnblockedOrThrow(),await super.evaluate(e,t,a,r,n)}async callFunction(e,t,a,r,n,s,o){return await this.#i.getContext(this.#B).targetUnblockedOrThrow(),await super.callFunction(e,t,a,r,n,s,o)}}Q.WindowRealm=se,Object.defineProperty(W,"__esModule",{value:!0}),W.serializeOrigin=W.BrowsingContextImpl=void 0;const oe=g,ie=J,ce=G,de=l,ue=Y,le=Q;class he{static LOGGER_PREFIX=`${de.LogType.debug}:browsingContext`;#u;userContext;#L;#Z=new Set;#i;#U={DOMContentLoaded:new ce.Deferred,load:new ce.Deferred};#F={withinDocument:new ce.Deferred};#q="about:blank";#y;#C;#V;#$;#K;#z;#t;constructor(e,t,a,r,n,s,o,i,c){this.#$=e,this.#C=t,this.#u=a,this.#L=r,this.userContext=n,this.#y=s,this.#i=o,this.#z=i,this.#t=c}static create(e,t,a,r,n,s,o,i,c){const d=new he(e,t,a,r,n,s,o,i,c);return d.#H(),o.addContext(d),d.isTopLevelContext()||d.parent.addChild(d.id),s.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.ContextCreated,params:d.serializeToBidiValue()},d.id),d}static getTimestamp(){return(new Date).getTime()}get navigableId(){return this.#V}dispose(){this.#W(),this.#C.deleteRealms({browsingContextId:this.id}),this.isTopLevelContext()||this.parent.#Z.delete(this.id),this.#J(),this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.ContextDestroyed,params:this.serializeToBidiValue()},this.id),this.#i.deleteContextById(this.id)}get id(){return this.#u}get parentId(){return this.#L}get parent(){return null===this.parentId?null:this.#i.getContext(this.parentId)}get directChildren(){return[...this.#Z].map((e=>this.#i.getContext(e)))}get allChildren(){const e=this.directChildren;return e.concat(...e.map((e=>e.allChildren)))}isTopLevelContext(){return null===this.#L}get top(){let e=this,t=e.parent;for(;t;)e=t,t=e.parent;return e}addChild(e){this.#Z.add(e)}#W(){this.directChildren.map((e=>e.dispose()))}get#G(){return(0,ie.assert)(this.#K,`No default realm for browsing context ${this.#u}`),this.#K}get cdpTarget(){return this.#$}updateCdpTarget(e){this.#$=e,this.#H()}get url(){return this.#q}async lifecycleLoaded(){await this.#U.load}async targetUnblockedOrThrow(){const e=await this.#$.unblocked;if("error"===e.kind)throw e.error}async getOrCreateSandbox(e){if(void 0===e||""===e)return this.#G;let t=this.#C.findRealms({browsingContextId:this.id,sandbox:e});return 0===t.length&&(await this.#$.cdpClient.sendCommand("Page.createIsolatedWorld",{frameId:this.id,worldName:e}),t=this.#C.findRealms({browsingContextId:this.id,sandbox:e}),(0,ie.assert)(0!==t.length)),t[0]}serializeToBidiValue(e=0,t=!0){return{context:this.#u,url:this.url,userContext:this.userContext,children:e>0?this.directChildren.map((t=>t.serializeToBidiValue(e-1,!1))):null,...t?{parent:this.#L}:{}}}onTargetInfoChanged(e){this.#q=e.targetInfo.url}#H(){this.#$.cdpClient.on("Page.frameNavigated",(e=>{this.id===e.frame.id&&(this.#q=e.frame.url+(e.frame.urlFragment??""),this.#W())})),this.#$.cdpClient.on("Page.navigatedWithinDocument",(e=>{if(this.id!==e.frameId)return;const t=he.getTimestamp();this.#q=e.url,this.#F.withinDocument.resolve(e),this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.FragmentNavigated,params:{context:this.id,navigation:null,timestamp:t,url:this.#q}},this.id)})),this.#$.cdpClient.on("Page.frameStartedLoading",(e=>{this.id===e.frameId&&this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.NavigationStarted,params:{context:this.id,navigation:null,timestamp:he.getTimestamp(),url:""}},this.id)})),this.#$.cdpClient.on("Page.lifecycleEvent",(e=>{if(this.id!==e.frameId)return;if("init"===e.name)return void this.#X(e.loaderId);if("commit"===e.name)return void(this.#V=e.loaderId);if(e.loaderId!==this.#V)return;const t=he.getTimestamp();switch(e.name){case"DOMContentLoaded":this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.DomContentLoaded,params:{context:this.id,navigation:this.#V??null,timestamp:t,url:this.#q}},this.id),this.#U.DOMContentLoaded.resolve(e);break;case"load":this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.Load,params:{context:this.id,navigation:this.#V??null,timestamp:t,url:this.#q}},this.id),this.#U.load.resolve(e)}})),this.#$.cdpClient.on("Runtime.executionContextCreated",(e=>{const{auxData:t,name:a,uniqueId:r,id:n}=e.context;if(!t||t.frameId!==this.id)return;let s,o;switch(t.type){case"isolated":o=a,s=this.#G.origin;break;case"default":s=pe(e.context.origin);break;default:return}const i=new le.WindowRealm(this.id,this.#i,this.#$.cdpClient,this.#y,n,this.#t,s,r,this.#C,o,this.#z);t.isDefault&&(this.#K=i,Promise.all(this.#$.getChannels().map((e=>e.startListenerFromWindow(i,this.#y)))))})),this.#$.cdpClient.on("Runtime.executionContextDestroyed",(e=>{this.#C.deleteRealms({cdpSessionId:this.#$.cdpSessionId,executionContextId:e.executionContextId})})),this.#$.cdpClient.on("Runtime.executionContextsCleared",(()=>{this.#C.deleteRealms({cdpSessionId:this.#$.cdpSessionId})})),this.#$.cdpClient.on("Page.javascriptDialogClosed",(e=>{const t=e.result;this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.UserPromptClosed,params:{context:this.id,accepted:t,userText:t&&e.userInput?e.userInput:void 0}},this.id)})),this.#$.cdpClient.on("Page.javascriptDialogOpening",(e=>{this.#y.registerEvent({type:"event",method:oe.ChromiumBidi.BrowsingContext.EventNames.UserPromptOpened,params:{context:this.id,type:e.type,message:e.message,defaultValue:e.defaultPrompt||void 0}},this.id)}))}#X(e){void 0!==e&&this.#V!==e?(this.#Y(),this.#V=e):this.#F.withinDocument.isFinished?this.#F.withinDocument=new ce.Deferred:this.#t?.(he.LOGGER_PREFIX,"Document changed (navigatedWithinDocument)")}#Y(){this.#U.DOMContentLoaded.isFinished?this.#U.DOMContentLoaded=new ce.Deferred:this.#t?.(he.LOGGER_PREFIX,"Document changed (DOMContentLoaded)"),this.#U.load.isFinished?this.#U.load=new ce.Deferred:this.#t?.(he.LOGGER_PREFIX,"Document changed (load)")}#J(){this.#U.DOMContentLoaded.isFinished||this.#U.DOMContentLoaded.reject(new oe.UnknownErrorException("navigation canceled")),this.#U.load.isFinished||this.#U.load.reject(new oe.UnknownErrorException("navigation canceled"))}async navigate(e,t){try{new URL(e)}catch{throw new oe.InvalidArgumentException(`Invalid URL: ${e}`)}await this.targetUnblockedOrThrow();const a=await this.#$.cdpClient.sendCommand("Page.navigate",{url:e,frameId:this.id});if(a.errorText)throw new oe.UnknownErrorException(a.errorText);switch(this.#X(a.loaderId),t){case"none":break;case"interactive":void 0===a.loaderId?await this.#F.withinDocument:await this.#U.DOMContentLoaded;break;case"complete":void 0===a.loaderId?await this.#F.withinDocument:await this.#U.load}return{navigation:a.loaderId??null,url:"none"===t?e:this.#q}}async reload(e,t){switch(await this.targetUnblockedOrThrow(),await this.#$.cdpClient.sendCommand("Page.reload",{ignoreCache:e}),this.#Y(),t){case"none":break;case"interactive":await this.#U.DOMContentLoaded;break;case"complete":await this.#U.load}return{navigation:"none"===t?null:this.navigableId??null,url:this.url}}async setViewport(e,t){if(null===e&&null===t)await this.#$.cdpClient.sendCommand("Emulation.clearDeviceMetricsOverride");else try{await this.#$.cdpClient.sendCommand("Emulation.setDeviceMetricsOverride",{width:e?e.width:0,height:e?e.height:0,deviceScaleFactor:t||0,mobile:!1,dontSetVisibleSize:!0})}catch(e){if(e.message.startsWith("Width and height values must be positive"))throw new oe.UnsupportedOperationException("Provided viewport dimensions are not supported");throw e}}async handleUserPrompt(e){await this.#$.cdpClient.sendCommand("Page.handleJavaScriptDialog",{accept:e.accept??!0,promptText:e.userText})}async activate(){await this.#$.cdpClient.sendCommand("Page.bringToFront")}async captureScreenshot(e){if(!this.isTopLevelContext())throw new oe.UnsupportedOperationException(`Non-top-level 'context' (${e.context}) is currently not supported`);const t=function(e){const{quality:t,type:a}=e.format??{type:"image/png"};switch(a){case"image/png":return{format:"png"};case"image/jpeg":return{format:"jpeg",...void 0===t?{}:{quality:Math.round(100*t)}};case"image/webp":return{format:"webp",...void 0===t?{}:{quality:Math.round(100*t)}}}throw new oe.InvalidArgumentException(`Image format '${a}' is not a supported format`)}(e);await this.#$.cdpClient.sendCommand("Page.bringToFront");let a,r=!1;switch(e.origin??="viewport",e.origin){case"document":a=String((()=>{const e=document.documentElement;return{x:0,y:0,width:e.scrollWidth,height:e.scrollHeight}})),r=!0;break;case"viewport":a=String((()=>{const e=window.visualViewport;return{x:e.pageLeft,y:e.pageTop,width:e.width,height:e.height}}))}const n=await this.getOrCreateSandbox(void 0),s=await n.callFunction(a,{type:"undefined"},[],!1,"none",{},!1);(0,ie.assert)("success"===s.type);const o=me(s.result);(0,ie.assert)(o);const i=e.clip?function(e,t){e=fe(e),t=fe(t);const a=Math.max(e.x,t.x),r=Math.max(e.y,t.y);return{x:a,y:r,width:Math.max(Math.min(e.x+e.width,t.x+t.width)-a,0),height:Math.max(Math.min(e.y+e.height,t.y+t.height)-r,0)}}(await this.#Q(e.clip),o):o;if(0===i.width||0===i.height)throw new oe.UnableToCaptureScreenException(`Unable to capture screenshot with zero dimensions: width=${i.width}, height=${i.height}`);return await this.#$.cdpClient.sendCommand("Page.captureScreenshot",{clip:{...i,scale:1},...t,captureBeyondViewport:r})}async print(e){const t={};if(void 0!==e.background&&(t.printBackground=e.background),void 0!==e.margin?.bottom&&(t.marginBottom=(0,ue.inchesFromCm)(e.margin.bottom)),void 0!==e.margin?.left&&(t.marginLeft=(0,ue.inchesFromCm)(e.margin.left)),void 0!==e.margin?.right&&(t.marginRight=(0,ue.inchesFromCm)(e.margin.right)),void 0!==e.margin?.top&&(t.marginTop=(0,ue.inchesFromCm)(e.margin.top)),void 0!==e.orientation&&(t.landscape="landscape"===e.orientation),void 0!==e.page?.height&&(t.paperHeight=(0,ue.inchesFromCm)(e.page.height)),void 0!==e.page?.width&&(t.paperWidth=(0,ue.inchesFromCm)(e.page.width)),void 0!==e.pageRanges){for(const t of e.pageRanges){if("number"==typeof t)continue;const e=t.split("-");if(e.length<1||e.length>2)throw new oe.InvalidArgumentException(`Invalid page range: ${t} is not a valid integer range.`);if(1===e.length){ge(e[0]??"");continue}let a,r;const[n="",s=""]=e;if(a=""===n?1:ge(n),r=""===s?Number.MAX_SAFE_INTEGER:ge(s),a>r)throw new oe.InvalidArgumentException(`Invalid page range: ${n} > ${s}`)}t.pageRanges=e.pageRanges.join(",")}void 0!==e.scale&&(t.scale=e.scale),void 0!==e.shrinkToFit&&(t.preferCSSPageSize=!e.shrinkToFit);try{return{data:(await this.#$.cdpClient.sendCommand("Page.printToPDF",t)).data}}catch(e){if("invalid print parameters: content area is empty"===e.message)throw new oe.UnsupportedOperationException(e.message);throw e}}async#Q(e){switch(e.type){case"box":return{x:e.x,y:e.y,width:e.width,height:e.height};case"element":{const t=await this.getOrCreateSandbox(void 0),a=await t.callFunction(String((e=>e instanceof Element)),{type:"undefined"},[e.element],!1,"none",{});if("exception"===a.type)throw new oe.NoSuchElementException(`Element '${e.element.sharedId}' was not found`);if((0,ie.assert)("boolean"===a.result.type),!a.result.value)throw new oe.NoSuchElementException(`Node '${e.element.sharedId}' is not an Element`);{const a=await t.callFunction(String((e=>{const t=e.getBoundingClientRect();return{x:t.x,y:t.y,height:t.height,width:t.width}})),{type:"undefined"},[e.element],!1,"none",{});(0,ie.assert)("success"===a.type);const r=me(a.result);if(!r)throw new oe.UnableToCaptureScreenException(`Could not get bounding box for Element '${e.element.sharedId}'`);return r}}}}async close(){await this.#$.cdpClient.sendCommand("Page.close")}async traverseHistory(e){if(0===e)return;const t=await this.#$.cdpClient.sendCommand("Page.getNavigationHistory"),a=t.entries[t.currentIndex+e];if(!a)throw new oe.NoSuchHistoryEntryException(`No history entry at delta ${e}`);await this.#$.cdpClient.sendCommand("Page.navigateToHistoryEntry",{entryId:a.id})}}function pe(e){return["://",""].includes(e)&&(e="null"),e}function me(e){if("object"!==e.type||void 0===e.value)return;const t=e.value.find((([e])=>"x"===e))?.[1],a=e.value.find((([e])=>"y"===e))?.[1],r=e.value.find((([e])=>"height"===e))?.[1],n=e.value.find((([e])=>"width"===e))?.[1];return"number"===t?.type&&"number"===a?.type&&"number"===r?.type&&"number"===n?.type?{x:t.value,y:a.value,width:n.value,height:r.value}:void 0}function fe(e){return{...e.width<0?{x:e.x+e.width,width:-e.width}:{x:e.x,width:e.width},...e.height<0?{y:e.y+e.height,height:-e.height}:{y:e.y,height:e.height}}}function ge(e){if(e=e.trim(),!/^[0-9]+$/.test(e))throw new oe.InvalidArgumentException(`Invalid integer: ${e}`);return parseInt(e)}W.BrowsingContextImpl=he,W.serializeOrigin=pe;var ye={},Se={},ve={};Object.defineProperty(ve,"__esModule",{value:!0}),ve.getRemoteValuesText=ve.logMessageFormatter=void 0;const we=J,Ce=["%s","%d","%i","%f","%o","%O","%c"];function be(e){return Ce.some((t=>e.includes(t)))}function xe(e){let t="";const a=e[0].value.toString(),r=e.slice(1,void 0),n=a.split(new RegExp(Ce.map((e=>`(${e})`)).join("|"),"g"));for(const a of n)if(void 0!==a&&""!==a)if(be(a)){const n=r.shift();(0,we.assert)(n,`Less value is provided: "${ke(e,!1)}"`),"%s"===a?t+=Ie(n):"%d"===a||"%i"===a?"bigint"===n.type||"number"===n.type||"string"===n.type?t+=parseInt(n.value.toString(),10):t+="NaN":"%f"===a?"bigint"===n.type||"number"===n.type||"string"===n.type?t+=parseFloat(n.value.toString()):t+="NaN":t+=Pe(n)}else t+=a;if(r.length>0)throw new Error(`More value is provided: "${ke(e,!1)}"`);return t}function Pe(e){if("array"!==e.type&&"bigint"!==e.type&&"date"!==e.type&&"number"!==e.type&&"object"!==e.type&&"string"!==e.type)return Ie(e);if("bigint"===e.type)return`${e.value.toString()}n`;if("number"===e.type)return e.value.toString();if(["date","string"].includes(e.type))return JSON.stringify(e.value);if("object"===e.type)return`{${e.value.map((e=>`${JSON.stringify(e[0])}:${Pe(e[1])}`)).join(",")}}`;if("array"===e.type)return`[${e.value?.map((e=>Pe(e))).join(",")??""}]`;throw Error(`Invalid value type: ${e}`)}function Ie(e){if(!Object.hasOwn(e,"value"))return e.type;switch(e.type){case"string":case"number":case"boolean":case"bigint":return String(e.value);case"regexp":return`/${e.value.pattern}/${e.value.flags??""}`;case"date":return new Date(e.value).toString();case"object":return`Object(${e.value?.length??""})`;case"array":return`Array(${e.value?.length??""})`;case"map":return`Map(${e.value?.length})`;case"set":return`Set(${e.value?.length})`;default:return e.type}}function ke(e,t){const a=e[0];return a?"string"===a.type&&be(a.value.toString())&&t?xe(e):e.map((e=>Ie(e))).join(" "):""}ve.logMessageFormatter=xe,ve.getRemoteValuesText=ke,Object.defineProperty(Se,"__esModule",{value:!0}),Se.LogManager=void 0;const Re=g,Ee=l,_e=ve;function Te(e){const t=e?.callFrames.map((e=>({columnNumber:e.columnNumber,functionName:e.functionName,lineNumber:e.lineNumber,url:e.url})));return t?{callFrames:t}:void 0}class Ne{#y;#C;#$;#t;constructor(e,t,a,r){this.#$=e,this.#C=t,this.#y=a,this.#t=r}static create(e,t,a,r){const n=new Ne(e,t,a,r);return n.#ee(),n}#ee(){this.#$.cdpClient.on("Runtime.consoleAPICalled",(e=>{const t=this.#C.findRealm({cdpSessionId:this.#$.cdpSessionId,executionContextId:e.executionContextId});if(void 0===t)return void this.#t?.(Ee.LogType.cdp,e);const a=void 0===t?Promise.resolve(e.args):Promise.all(e.args.map((e=>t.serializeCdpObject(e,"none"))));for(const r of t.associatedBrowsingContexts)this.#y.registerPromiseEvent(a.then((a=>{return{kind:"success",value:{type:"event",method:Re.ChromiumBidi.Log.EventNames.LogEntryAdded,params:{level:(r=e.type,["error","assert"].includes(r)?"error":["debug","trace"].includes(r)?"debug":["warn","warning"].includes(r)?"warn":"info"),source:t.source,text:(0,_e.getRemoteValuesText)(a,!0),timestamp:Math.round(e.timestamp),stackTrace:Te(e.stackTrace),type:"console",method:"warning"===e.type?"warn":e.type,args:a}}};var r})),r.id,Re.ChromiumBidi.Log.EventNames.LogEntryAdded)})),this.#$.cdpClient.on("Runtime.exceptionThrown",(e=>{const t=this.#C.findRealm({cdpSessionId:this.#$.cdpSessionId,executionContextId:e.exceptionDetails.executionContextId});if(void 0!==t)for(const a of t.associatedBrowsingContexts)this.#y.registerPromiseEvent(Ne.#te(e,t).then((a=>({kind:"success",value:{type:"event",method:Re.ChromiumBidi.Log.EventNames.LogEntryAdded,params:{level:"error",source:t.source,text:a,timestamp:Math.round(e.timestamp),stackTrace:Te(e.exceptionDetails.stackTrace),type:"javascript"}}}))),a.id,Re.ChromiumBidi.Log.EventNames.LogEntryAdded);else this.#t?.(Ee.LogType.cdp,e)}))}static async#te(e,t){return e.exceptionDetails.exception?void 0===t?JSON.stringify(e.exceptionDetails.exception):await t.stringifyObject(e.exceptionDetails.exception):e.exceptionDetails.text}}Se.LogManager=Ne;var je={},Oe={},Me={};Object.defineProperty(Me,"__esModule",{value:!0}),Me.bidiToCdpCookie=Me.cdpToBiDiCookie=Me.cdpAuthChallengeResponseFromBidiAuthContinueWithAuthAction=Me.cdpFetchHeadersFromBidiNetworkHeaders=Me.bidiNetworkHeadersFromCdpFetchHeaders=Me.cdpNetworkHeadersFromBidiNetworkHeaders=Me.bidiNetworkHeadersFromCdpNetworkHeaders=Me.computeHeadersSize=void 0;const Ae=I;function Be(e){switch(e){case"Strict":return"strict";case"None":return"none";default:return"lax"}}function ze(e){switch(e){case"strict":return"Strict";case"lax":return"Lax";case"none":return"None"}throw new Ae.InvalidArgumentException(`Unknown 'sameSite' value ${e}`)}Me.computeHeadersSize=function(e){const t=e.reduce(((e,t)=>`${e}${t.name}: ${t.value.value}\r\n`),"");return(new TextEncoder).encode(t).length},Me.bidiNetworkHeadersFromCdpNetworkHeaders=function(e){return e?Object.entries(e).map((([e,t])=>({name:e,value:{type:"string",value:t}}))):[]},Me.cdpNetworkHeadersFromBidiNetworkHeaders=function(e){if(void 0!==e)return e.reduce(((e,t)=>(e[t.name]=t.value.value,e)),{})},Me.bidiNetworkHeadersFromCdpFetchHeaders=function(e){return e?e.map((({name:e,value:t})=>({name:e,value:{type:"string",value:t}}))):[]},Me.cdpFetchHeadersFromBidiNetworkHeaders=function(e){if(void 0!==e)return e.map((({name:e,value:t})=>({name:e,value:t.value})))},Me.cdpAuthChallengeResponseFromBidiAuthContinueWithAuthAction=function(e){switch(e){case"default":return"Default";case"cancel":return"CancelAuth";case"provideCredentials":return"ProvideCredentials"}},Me.cdpToBiDiCookie=function(e){const t={name:e.name,value:{type:"string",value:e.value},domain:e.domain,path:e.path,size:e.size,httpOnly:e.httpOnly,secure:e.secure,sameSite:void 0===e.sameSite?"none":Be(e.sameSite),...e.expires>=0?{expiry:e.expires}:void 0};return t["goog:session"]=e.session,t["goog:priority"]=e.priority,t["goog:sameParty"]=e.sameParty,t["goog:sourceScheme"]=e.sourceScheme,t["goog:sourcePort"]=e.sourcePort,void 0!==e.partitionKey&&(t["goog:partitionKey"]=e.partitionKey),void 0!==e.partitionKeyOpaque&&(t["goog:partitionKeyOpaque"]=e.partitionKeyOpaque),t},Me.bidiToCdpCookie=function(e,t){if("string"!==e.cookie.value.type)throw new Ae.UnsupportedOperationException("Only string cookie values are supported");const a=e.cookie.value.value,r={name:e.cookie.name,value:a,domain:e.cookie.domain,path:e.cookie.path??"/",secure:e.cookie.secure??!1,httpOnly:e.cookie.httpOnly??!1,...void 0!==t.sourceOrigin&&{partitionKey:t.sourceOrigin},...void 0!==e.cookie.expiry&&{expires:e.cookie.expiry},...void 0!==e.cookie.sameSite&&{sameSite:ze(e.cookie.sameSite)}};return void 0!==e.cookie["goog:url"]&&(r.url=e.cookie["goog:url"]),void 0!==e.cookie["goog:priority"]&&(r.priority=e.cookie["goog:priority"]),void 0!==e.cookie["goog:sameParty"]&&(r.sameParty=e.cookie["goog:sameParty"]),void 0!==e.cookie["goog:sourceScheme"]&&(r.sourceScheme=e.cookie["goog:sourceScheme"]),void 0!==e.cookie["goog:sourcePort"]&&(r.sourcePort=e.cookie["goog:sourcePort"]),r},Object.defineProperty(Oe,"__esModule",{value:!0}),Oe.NetworkRequest=void 0;const De=g,Le=J,Ze=G,Ue=Me;class Fe{static#ae="UNKNOWN";#re;#ne=void 0;#se=!1;#oe;#y;#ie;#ce={};#de={};#ue=new Ze.Deferred;#le=new Ze.Deferred;#he=new Ze.Deferred;#$;constructor(e,t,a,r,n=0){this.#re=e,this.#y=t,this.#ie=a,this.#$=r,this.#oe=n}get requestId(){return this.#re}get url(){return this.#de.info?.url??this.#ce.info?.request.url}get redirectCount(){return this.#oe}get cdpTarget(){return this.#$}isRedirecting(){return Boolean(this.#ce.info)}handleRedirect(e){this.#pe(),this.#me(),this.#de.hasExtraInfo=e.redirectHasExtraInfo,this.#de.info=e.redirectResponse,this.#fe(!0)}#fe(e=!1){const t=e||Boolean(this.#ce.extraInfo)||this.#se||Boolean(this.#de.info&&!this.#de.hasExtraInfo)||"beforeRequestSent"===this.#ne;this.#ce.info&&t&&this.#ue.resolve({kind:"success",value:void 0});const a=Boolean(this.#de.extraInfo)||this.#se||Boolean(this.#de.info&&!this.#de.hasExtraInfo)||"responseStarted"===this.#ne;this.#de.info&&a&&(this.#le.resolve({kind:"success",value:void 0}),this.#he.resolve({kind:"success",value:void 0}))}onRequestWillBeSentEvent(e){this.#ce.info=e,this.#ge(),this.#fe()}onRequestWillBeSentExtraInfoEvent(e){this.#ce.extraInfo=e,this.#fe()}onResponseReceivedExtraInfoEvent(e){this.#de.extraInfo=e,this.#fe()}onResponseReceivedEvent(e){this.#de.hasExtraInfo=e.hasExtraInfo,this.#de.info=e.response,this.#pe(),this.#me(),this.#fe()}onServedFromCache(){this.#se=!0,this.#fe()}onLoadingFailedEvent(e){this.#ue.resolve({kind:"success",value:void 0}),this.#le.resolve({kind:"error",error:new Error("Network event loading failed")}),this.#he.resolve({kind:"error",error:new Error("Network event loading failed")}),this.#y.registerEvent({type:"event",method:De.ChromiumBidi.Network.EventNames.FetchError,params:{...this.#ye(),errorText:e.errorText}},this.#Se)}onRequestPaused(e){if(this.#ve())return void this.continueRequest(e.requestId).catch((()=>{}));let t;t=void 0===e.responseErrorReason&&void 0===e.responseStatusCode?"beforeRequestSent":401===e.responseStatusCode&&"Unauthorized"===e.responseStatusText?"authRequired":"responseStarted";const a=(0,Ue.bidiNetworkHeadersFromCdpFetchHeaders)(e.responseHeaders);this.#ie.addBlockedRequest(this.requestId,{request:e.requestId,phase:t,response:{url:e.request.url,protocol:"",status:e.responseStatusCode??0,statusText:e.responseStatusText??"",fromCache:!1,headers:a,mimeType:"",bytesReceived:0,headersSize:(0,Ue.computeHeadersSize)(a),bodySize:0,content:{size:0},authChallenge:void 0}}),this.#ne=t,this.#fe()}async failRequest(e,t){await this.#$.cdpClient.sendCommand("Fetch.failRequest",{requestId:e,errorReason:t}),this.#ne=void 0}async continueRequest(e,t,a,r){await this.#$.cdpClient.sendCommand("Fetch.continueRequest",{requestId:e,url:t,method:a,headers:r}),this.#ne=void 0}async continueResponse(e,t,a,r){await this.#$.cdpClient.sendCommand("Fetch.continueResponse",{requestId:e,responseCode:t,responsePhrase:a,responseHeaders:r}),this.#ne=void 0}async continueWithAuth(e,t,a,r){await this.#$.cdpClient.sendCommand("Fetch.continueWithAuth",{requestId:e,authChallengeResponse:{response:t,username:a,password:r}}),this.#ne=void 0}async provideResponse(e,t,a,r,n){await this.#$.cdpClient.sendCommand("Fetch.fulfillRequest",{requestId:e,responseCode:t,responsePhrase:a,responseHeaders:r,...n?{body:btoa(n)}:{}}),this.#ne=void 0}dispose(){const e={kind:"error",error:new Error("Network processor detached")};this.#ue.resolve(e),this.#le.resolve(e),this.#he.resolve(e)}get#Se(){return this.#ce.info?.frameId??null}get statusCode(){return this.#de.info?.status??this.#de.extraInfo?.statusCode??-1}#ye(e){const t=void 0!==e&&e===this.#ne,a=this.#ie.getNetworkIntercepts(this.#re,e);return{isBlocked:t,context:this.#Se,navigation:this.#we(),redirectCount:this.#oe,request:this.#Ce(),timestamp:Math.round(1e3*(this.#ce.info?.wallTime??0)),intercepts:t?a:void 0}}#we(){return this.#ce.info&&this.#ce.info.loaderId&&this.#ce.info.loaderId===this.#ce.info.requestId?this.#ce.info.loaderId:null}#Ce(){const e=this.#ce.extraInfo?Fe.#be(this.#ce.extraInfo.associatedCookies):[],t=(0,Ue.bidiNetworkHeadersFromCdpNetworkHeaders)(this.#ce.info?.request.headers);return{request:this.#ce.info?.requestId??Fe.#ae,url:this.#ce.info?.request.url??Fe.#ae,method:this.#ce.info?.request.method??Fe.#ae,headers:t,cookies:e,headersSize:(0,Ue.computeHeadersSize)(t),bodySize:0,timings:this.#xe()}}#xe(){return{timeOrigin:0,requestTime:0,redirectStart:0,redirectEnd:0,fetchStart:0,dnsStart:0,dnsEnd:0,connectStart:0,connectEnd:0,tlsStart:0,requestStart:0,responseStart:0,responseEnd:0}}#ge(){this.#ve()||this.#y.registerPromiseEvent(this.#ue.then((e=>{if("success"===e.kind)try{return{kind:"success",value:Object.assign(this.#Pe(),{type:"event"})}}catch(e){return{kind:"error",error:e instanceof Error?e:new Error("Unknown")}}return e})),this.#Se,De.ChromiumBidi.Network.EventNames.BeforeRequestSent)}#Pe(){return(0,Le.assert)(this.#ce.info,"RequestWillBeSentEvent is not set"),{method:De.ChromiumBidi.Network.EventNames.BeforeRequestSent,params:{...this.#ye("beforeRequestSent"),initiator:{type:Fe.#Ie(this.#ce.info.initiator.type)}}}}#pe(){this.#ve()||this.#y.registerPromiseEvent(this.#le.then((e=>{if("success"===e.kind)try{return{kind:"success",value:Object.assign(this.#ke(),{type:"event"})}}catch(e){return{kind:"error",error:e instanceof Error?e:new Error("Unknown")}}return e})),this.#Se,De.ChromiumBidi.Network.EventNames.ResponseStarted)}#ke(){(0,Le.assert)(this.#ce.info,"RequestWillBeSentEvent is not set"),(0,Le.assert)(this.#de.info,"ResponseReceivedEvent is not set"),this.#de.info.fromDiskCache&&(this.#de.extraInfo=void 0);const e=(0,Ue.bidiNetworkHeadersFromCdpNetworkHeaders)(this.#de.info.headers);return{method:De.ChromiumBidi.Network.EventNames.ResponseStarted,params:{...this.#ye(),response:{url:this.#de.info.url??Fe.#ae,protocol:this.#de.info.protocol??"",status:this.statusCode,statusText:this.#de.info.statusText,fromCache:this.#de.info.fromDiskCache||this.#de.info.fromPrefetchCache||this.#se,headers:e,mimeType:this.#de.info.mimeType,bytesReceived:this.#de.info.encodedDataLength,headersSize:(0,Ue.computeHeadersSize)(e),bodySize:0,content:{size:0}}}}}#me(){this.#ve()||this.#y.registerPromiseEvent(this.#he.then((e=>{if("success"===e.kind)try{return{kind:"success",value:Object.assign(this.#Re(),{type:"event"})}}catch(e){return{kind:"error",error:e instanceof Error?e:new Error("Unknown")}}return e})),this.#Se,De.ChromiumBidi.Network.EventNames.ResponseCompleted)}#Re(){(0,Le.assert)(this.#ce.info,"RequestWillBeSentEvent is not set"),(0,Le.assert)(this.#de.info,"ResponseReceivedEvent is not set"),this.#de.info.fromDiskCache&&(this.#de.extraInfo=void 0);const e=(0,Ue.bidiNetworkHeadersFromCdpNetworkHeaders)(this.#de.info.headers);return{method:De.ChromiumBidi.Network.EventNames.ResponseCompleted,params:{...this.#ye(),response:{url:this.#de.info.url??Fe.#ae,protocol:this.#de.info.protocol??"",status:this.statusCode,statusText:this.#de.info.statusText,fromCache:this.#de.info.fromDiskCache||this.#de.info.fromPrefetchCache||this.#se,headers:e,mimeType:this.#de.info.mimeType,bytesReceived:this.#de.info.encodedDataLength,headersSize:(0,Ue.computeHeadersSize)(e),bodySize:0,content:{size:0}}}}}#ve(){return this.#ce.info?.request.url.endsWith("/favicon.ico")??!1}static#Ie(e){switch(e){case"parser":case"script":case"preflight":return e;default:return"other"}}static#be(e){return e.filter((({blockedReasons:e})=>!Array.isArray(e)||0===e.length)).map((({cookie:e})=>(0,Ue.cdpToBiDiCookie)(e)))}}Oe.NetworkRequest=Fe,Object.defineProperty(je,"__esModule",{value:!0}),je.NetworkManager=void 0;const qe=Oe;class Ve{#$;#y;#ie;constructor(e,t,a){this.#$=e,this.#y=t,this.#ie=a}get cdpTarget(){return this.#$}#Ee(e,t){let a=this.#ie.getRequest(e);return a||(a=new qe.NetworkRequest(e,this.#y,this.#ie,this.#$,t),this.#ie.addRequest(a),a)}static create(e,t,a){const r=new Ve(e,t,a);return e.browserCdpClient.on("Target.detachedFromTarget",(t=>{e.cdpClient.sessionId===t.sessionId&&r.#ie.disposeRequestMap()})),e.cdpClient.on("Network.requestWillBeSent",(e=>{const t=r.#ie.getRequest(e.requestId);t&&t.isRedirecting()?(t.handleRedirect(e),r.#ie.deleteRequest(e.requestId),r.#Ee(e.requestId,t.redirectCount+1).onRequestWillBeSentEvent(e)):t?t.onRequestWillBeSentEvent(e):r.#Ee(e.requestId).onRequestWillBeSentEvent(e)})),e.cdpClient.on("Network.requestWillBeSentExtraInfo",(e=>{r.#Ee(e.requestId).onRequestWillBeSentExtraInfoEvent(e)})),e.cdpClient.on("Network.responseReceived",(e=>{r.#Ee(e.requestId).onResponseReceivedEvent(e)})),e.cdpClient.on("Network.responseReceivedExtraInfo",(e=>{r.#Ee(e.requestId).onResponseReceivedExtraInfoEvent(e)})),e.cdpClient.on("Network.requestServedFromCache",(e=>{r.#Ee(e.requestId).onServedFromCache()})),e.cdpClient.on("Network.loadingFailed",(e=>{r.#Ee(e.requestId).onLoadingFailedEvent(e)})),e.cdpClient.on("Fetch.requestPaused",(e=>{e.networkId&&r.#Ee(e.networkId).onRequestPaused(e)})),r}}je.NetworkManager=Ve,Object.defineProperty(ye,"__esModule",{value:!0}),ye.CdpTarget=void 0;const $e=G,Ke=Se,He=je;class We{#u;#g;#o;#y;#_e;#ie;#Te=new $e.Deferred;#Ne;static create(e,t,a,r,n,s,o,i,c){const d=new We(e,t,a,n,s,o,i);return Ke.LogManager.create(d,r,n,c),He.NetworkManager.create(d,n,o),d.#je(),d.#Oe(),d}constructor(e,t,a,r,n,s,o){this.#u=e,this.#g=t,this.#y=r,this.#_e=n,this.#ie=s,this.#o=a,this.#Ne=o}get unblocked(){return this.#Te}get id(){return this.#u}get cdpClient(){return this.#g}get browserCdpClient(){return this.#o}get cdpSessionId(){return this.#g.sessionId}async fetchEnable(){await this.#g.sendCommand("Fetch.enable",this.#ie.getFetchEnableParams())}async fetchDisable(){await this.#g.sendCommand("Fetch.disable")}async#Oe(){try{await Promise.all([this.#g.sendCommand("Runtime.enable"),this.#g.sendCommand("Page.enable"),this.#g.sendCommand("Page.setLifecycleEventsEnabled",{enabled:!0}),this.#g.sendCommand("Security.setIgnoreCertificateErrors",{ignore:this.#Ne}),this.#g.sendCommand("Network.enable"),this.fetchEnable(),this.#g.sendCommand("Target.setAutoAttach",{autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),this.#Me(),this.#g.sendCommand("Runtime.runIfWaitingForDebugger")])}catch(e){if(!this.#g.isCloseError(e))return void this.#Te.resolve({kind:"error",error:e})}this.#Te.resolve({kind:"success",value:void 0})}#je(){this.#g.on("*",((e,t)=>{"string"==typeof e&&this.#y.registerEvent({type:"event",method:`cdp.${e}`,params:{event:e,params:t,session:this.cdpSessionId}},null)}))}getChannels(){return this.#_e.find().flatMap((e=>e.channels))}async#Me(){for(const e of this.#_e.find({global:!0}))await e.initInTarget(this,!0)}}ye.CdpTarget=We,Object.defineProperty(j,"__esModule",{value:!0}),j.BrowsingContextProcessor=void 0;const Je=g,Ge=l,Xe=O,Ye=W,Qe=ye;j.BrowsingContextProcessor=class{#o;#c;#Ae;#y;#i;#ie;#Ne;#z;#_e;#C;#Be;#t;constructor(e,t,a,r,n,s,o,i,c,d,u,l){this.#Ne=c,this.#c=e,this.#o=t,this.#Ae=a,this.#y=r,this.#i=n,this.#_e=i,this.#ie=o,this.#C=s,this.#z=d,this.#Be=u,this.#t=l,this.#je(t)}getTree(e){return{contexts:(void 0===e.root?this.#i.getTopLevelContexts():[this.#i.getContext(e.root)]).map((t=>t.serializeToBidiValue(e.maxDepth??Number.MAX_VALUE)))}}async create(e){let t,a=e.userContext??"default";if(void 0!==e.referenceContext){if(t=this.#i.getContext(e.referenceContext),!t.isTopLevelContext())throw new Je.InvalidArgumentException("referenceContext should be a top-level context");a=t.userContext}let r,n=!1;switch(e.type){case"tab":n=!1;break;case"window":n=!0}if("default"!==a){const e=this.#i.getAllContexts().filter((e=>e.userContext===a));e.length||(n=!0)}try{r=await this.#o.sendCommand("Target.createTarget",{url:"about:blank",newWindow:n,browserContextId:"default"===a?void 0:a})}catch(e){if(e.message.startsWith("Failed to find browser context with id")||"browserContextId"===e.message)throw new Je.NoSuchUserContextException(`The context ${a} was not found`);throw e}const s=r.targetId,o=this.#i.getContext(s);return await o.lifecycleLoaded(),{context:o.id}}navigate(e){return this.#i.getContext(e.context).navigate(e.url,e.wait??"none")}reload(e){return this.#i.getContext(e.context).reload(e.ignoreCache??!1,e.wait??"none")}async activate(e){const t=this.#i.getContext(e.context);if(!t.isTopLevelContext())throw new Je.InvalidArgumentException("Activation is only supported on the top-level context");return await t.activate(),{}}async captureScreenshot(e){const t=this.#i.getContext(e.context);return await t.captureScreenshot(e)}async print(e){const t=this.#i.getContext(e.context);return await t.print(e)}async setViewport(e){const t=this.#i.getContext(e.context);if(!t.isTopLevelContext())throw new Je.InvalidArgumentException("Emulating viewport is only supported on the top-level context");return await t.setViewport(e.viewport,e.devicePixelRatio),{}}async traverseHistory(e){const t=this.#i.getContext(e.context);if(!t)throw new Je.InvalidArgumentException(`No browsing context with id ${e.context}`);return await t.traverseHistory(e.delta),{}}async handleUserPrompt(e){const t=this.#i.getContext(e.context);return await t.handleUserPrompt(e),{}}async close(e){const t=this.#i.getContext(e.context);if(!t.isTopLevelContext())throw new Je.InvalidArgumentException(`Non top-level browsing context ${t.id} cannot be closed.`);try{const a=new Promise((t=>{const a=r=>{r.targetId===e.context&&(this.#o.off("Target.detachedFromTarget",a),t())};this.#o.on("Target.detachedFromTarget",a)}));e.promptUnload?await t.close():await this.#o.sendCommand("Target.closeTarget",{targetId:e.context}),await a}catch(e){if(-32e3!==e.code||"Not attached to an active page"!==e.message)throw e}return{}}#je(e){e.on("Target.attachedToTarget",(t=>{this.#ze(t,e)})),e.on("Target.detachedFromTarget",(e=>{this.#De(e)})),e.on("Target.targetInfoChanged",(e=>{this.#Le(e)})),e.on("Page.frameAttached",(e=>{this.#Ze(e)})),e.on("Page.frameDetached",(e=>{this.#Ue(e)}))}#Ze(e){const t=this.#i.findContext(e.parentFrameId);void 0!==t&&Ye.BrowsingContextImpl.create(t.cdpTarget,this.#C,e.frameId,e.parentFrameId,t.userContext,this.#y,this.#i,this.#z,this.#t)}#Ue(e){"swap"!==e.reason&&this.#i.findContext(e.frameId)?.dispose()}#ze(e,t){const{sessionId:a,targetInfo:r}=e,n=this.#c.getCdpClient(a);switch(this.#t?.(Ge.LogType.debugInfo,"AttachedToTarget event received:",e),r.type){case"page":case"iframe":{if(r.targetId===this.#Ae)break;const e=this.#Fe(n,r),t=this.#i.findContext(r.targetId);return void(t?t.updateCdpTarget(e):Ye.BrowsingContextImpl.create(e,this.#C,r.targetId,null,r.browserContextId&&r.browserContextId!==this.#Be?r.browserContextId:"default",this.#y,this.#i,this.#z,this.#t))}case"worker":{const e=t.sessionId&&this.#i.findContextBySession(t.sessionId);if(!e)break;const a=this.#Fe(n,r);return void this.#qe(a,this.#C.getRealm({browsingContextId:e.id,type:"window",sandbox:void 0}))}}n.sendCommand("Runtime.runIfWaitingForDebugger").then((()=>t.sendCommand("Target.detachFromTarget",e))).catch((e=>this.#t?.(Ge.LogType.debugError,e)))}#Fe(e,t){return this.#je(e),Qe.CdpTarget.create(t.targetId,e,this.#o,this.#C,this.#y,this.#_e,this.#ie,this.#Ne,this.#t)}#Ve=new Map;#qe(e,t){e.cdpClient.on("Runtime.executionContextCreated",(a=>{const{uniqueId:r,id:n,origin:s}=a.context,o=new Xe.DedicatedWorkerRealm(e.cdpClient,this.#y,n,this.#t,(0,Ye.serializeOrigin)(s),t,r,this.#C);this.#Ve.set(e.cdpSessionId,o)}))}#De(e){const t=this.#i.findContextBySession(e.sessionId);if(t)return t.dispose(),void this.#_e.find({targetId:t.id}).map((e=>e.dispose(t.id)));const a=this.#Ve.get(e.sessionId);a&&this.#C.deleteRealms({cdpSessionId:a.cdpClient.sessionId})}#Le(e){const t=this.#i.findContext(e.targetInfo.targetId);t&&t.onTargetInfoChanged(e)}};var et={},tt={},at={};Object.defineProperty(at,"__esModule",{value:!0}),at.WheelSource=at.PointerSource=at.KeySource=at.NoneSource=void 0;at.NoneSource=class{type="none"};at.KeySource=class{type="key";pressed=new Set;#$e=0;get modifiers(){return this.#$e}get alt(){return 1==(1&this.#$e)}set alt(e){this.#Ke(e,1)}get ctrl(){return 2==(2&this.#$e)}set ctrl(e){this.#Ke(e,2)}get meta(){return 4==(4&this.#$e)}set meta(e){this.#Ke(e,4)}get shift(){return 8==(8&this.#$e)}set shift(e){this.#Ke(e,8)}#Ke(e,t){e?this.#$e|=t:this.#$e&=~t}};at.PointerSource=class{type="pointer";subtype;pointerId;pressed=new Set;x=0;y=0;constructor(e,t){this.pointerId=e,this.subtype=t}get buttons(){let e=0;for(const t of this.pressed)switch(t){case 0:e|=1;break;case 1:e|=4;break;case 2:e|=2;break;case 3:e|=8;break;case 4:e|=16}return e}static ClickContext=class e{static#He=500;static#We=2;count=0;#Je;#Ge;#Xe;constructor(e,t,a){this.#Je=e,this.#Ge=t,this.#Xe=a}compare(t){return t.#Xe-this.#Xe>e.#He||Math.abs(t.#Je-this.#Je)>e.#We||Math.abs(t.#Ge-this.#Ge)>e.#We}};#Ye=new Map;setClickCount(e,t){let a=this.#Ye.get(e);return a&&!a.compare(t)||(a=t),++a.count,this.#Ye.set(e,a),a.count}getClickCount(e){return this.#Ye.get(e)?.count??0}};at.WheelSource=class{type="wheel"};var rt={};Object.defineProperty(rt,"__esModule",{value:!0}),rt.getKeyLocation=rt.getKeyCode=rt.getNormalizedKey=void 0,rt.getNormalizedKey=function(e){switch(e){case"\ue000":return"Unidentified";case"\ue001":return"Cancel";case"\ue002":return"Help";case"\ue003":return"Backspace";case"\ue004":return"Tab";case"\ue005":return"Clear";case"\ue006":return"Return";case"\ue007":return"Enter";case"\ue008":case"\ue050":return"Shift";case"\ue009":case"\ue051":return"Control";case"\ue00a":case"\ue052":return"Alt";case"\ue00b":return"Pause";case"\ue00c":return"Escape";case"\ue00d":return" ";case"\ue00e":case"\ue054":return"PageUp";case"\ue00f":case"\ue055":return"PageDown";case"\ue010":case"\ue056":return"End";case"\ue011":case"\ue057":return"Home";case"\ue012":case"\ue058":return"ArrowLeft";case"\ue013":case"\ue059":return"ArrowUp";case"\ue014":case"\ue05a":return"ArrowRight";case"\ue015":case"\ue05b":return"ArrowDown";case"\ue016":case"\ue05c":return"Insert";case"\ue017":case"\ue05d":return"Delete";case"\ue018":return";";case"\ue019":return"=";case"\ue01a":return"0";case"\ue01b":return"1";case"\ue01c":return"2";case"\ue01d":return"3";case"\ue01e":return"4";case"\ue01f":return"5";case"\ue020":return"6";case"\ue021":return"7";case"\ue022":return"8";case"\ue023":return"9";case"\ue024":return"*";case"\ue025":return"+";case"\ue026":return",";case"\ue027":return"-";case"\ue028":return".";case"\ue029":return"/";case"\ue031":return"F1";case"\ue032":return"F2";case"\ue033":return"F3";case"\ue034":return"F4";case"\ue035":return"F5";case"\ue036":return"F6";case"\ue037":return"F7";case"\ue038":return"F8";case"\ue039":return"F9";case"\ue03a":return"F10";case"\ue03b":return"F11";case"\ue03c":return"F12";case"\ue03d":case"\ue053":return"Meta";case"\ue040":return"ZenkakuHankaku";default:return e}},rt.getKeyCode=function(e){switch(e){case"`":case"~":return"Backquote";case"\\":case"|":return"Backslash";case"\ue003":return"Backspace";case"[":case"{":return"BracketLeft";case"]":case"}":return"BracketRight";case",":case"<":return"Comma";case"0":case")":return"Digit0";case"1":case"!":return"Digit1";case"2":case"@":return"Digit2";case"3":case"#":return"Digit3";case"4":case"$":return"Digit4";case"5":case"%":return"Digit5";case"6":case"^":return"Digit6";case"7":case"&":return"Digit7";case"8":case"*":return"Digit8";case"9":case"(":return"Digit9";case"=":case"+":return"Equal";case"a":case"A":return"KeyA";case"b":case"B":return"KeyB";case"c":case"C":return"KeyC";case"d":case"D":return"KeyD";case"e":case"E":return"KeyE";case"f":case"F":return"KeyF";case"g":case"G":return"KeyG";case"h":case"H":return"KeyH";case"i":case"I":return"KeyI";case"j":case"J":return"KeyJ";case"k":case"K":return"KeyK";case"l":case"L":return"KeyL";case"m":case"M":return"KeyM";case"n":case"N":return"KeyN";case"o":case"O":return"KeyO";case"p":case"P":return"KeyP";case"q":case"Q":return"KeyQ";case"r":case"R":return"KeyR";case"s":case"S":return"KeyS";case"t":case"T":return"KeyT";case"u":case"U":return"KeyU";case"v":case"V":return"KeyV";case"w":case"W":return"KeyW";case"x":case"X":return"KeyX";case"y":case"Y":return"KeyY";case"z":case"Z":return"KeyZ";case"-":case"_":return"Minus";case".":return"Period";case"'":case'"':return"Quote";case";":case":":return"Semicolon";case"/":case"?":return"Slash";case"\ue00a":return"AltLeft";case"\ue052":return"AltRight";case"\ue009":return"ControlLeft";case"\ue051":return"ControlRight";case"\ue006":return"Enter";case"\ue03d":return"MetaLeft";case"\ue053":return"MetaRight";case"\ue008":return"ShiftLeft";case"\ue050":return"ShiftRight";case" ":case"\ue00d":return"Space";case"\ue004":return"Tab";case"\ue017":return"Delete";case"\ue010":return"End";case"\ue002":return"Help";case"\ue011":return"Home";case"\ue016":return"Insert";case"\ue00f":return"PageDown";case"\ue00e":return"PageUp";case"\ue015":return"ArrowDown";case"\ue012":return"ArrowLeft";case"\ue014":return"ArrowRight";case"\ue013":return"ArrowUp";case"\ue00c":return"Escape";case"\ue031":return"F1";case"\ue032":return"F2";case"\ue033":return"F3";case"\ue034":return"F4";case"\ue035":return"F5";case"\ue036":return"F6";case"\ue037":return"F7";case"\ue038":return"F8";case"\ue039":return"F9";case"\ue03a":return"F10";case"\ue03b":return"F11";case"\ue03c":return"F12";case"\ue01a":case"\ue05c":return"Numpad0";case"\ue01b":case"\ue056":return"Numpad1";case"\ue01c":case"\ue05b":return"Numpad2";case"\ue01d":case"\ue055":return"Numpad3";case"\ue01e":case"\ue058":return"Numpad4";case"\ue01f":return"Numpad5";case"\ue020":case"\ue05a":return"Numpad6";case"\ue021":case"\ue057":return"Numpad7";case"\ue022":case"\ue059":return"Numpad8";case"\ue023":case"\ue054":return"Numpad9";case"\ue025":return"NumpadAdd";case"\ue026":return"NumpadComma";case"\ue028":case"\ue05d":return"NumpadDecimal";case"\ue029":return"NumpadDivide";case"\ue007":return"NumpadEnter";case"\ue024":return"NumpadMultiply";case"\ue027":return"NumpadSubtract";default:return}},rt.getKeyLocation=function(e){switch(e){case"\ue007":case"\ue008":case"\ue009":case"\ue00a":case"\ue03d":return 1;case"\ue01a":case"\ue01b":case"\ue01c":case"\ue01d":case"\ue01e":case"\ue01f":case"\ue020":case"\ue021":case"\ue022":case"\ue023":case"\ue024":case"\ue025":case"\ue026":case"\ue027":case"\ue028":case"\ue029":case"\ue054":case"\ue055":case"\ue056":case"\ue057":case"\ue058":case"\ue059":case"\ue05a":case"\ue05b":case"\ue05c":case"\ue05d":return 3;case"\ue050":case"\ue051":case"\ue052":case"\ue053":return 2;default:return 0}};var nt={};Object.defineProperty(nt,"__esModule",{value:!0}),nt.KeyToKeyCode=void 0,nt.KeyToKeyCode={0:48,1:49,2:50,3:51,4:52,5:53,6:54,7:55,8:56,9:57,Abort:3,Help:6,Backspace:8,Tab:9,Numpad5:12,NumpadEnter:13,Enter:13,"\\r":13,"\\n":13,ShiftLeft:16,ShiftRight:16,ControlLeft:17,ControlRight:17,AltLeft:18,AltRight:18,Pause:19,CapsLock:20,Escape:27,Convert:28,NonConvert:29,Space:32,Numpad9:33,PageUp:33,Numpad3:34,PageDown:34,End:35,Numpad1:35,Home:36,Numpad7:36,ArrowLeft:37,Numpad4:37,Numpad8:38,ArrowUp:38,ArrowRight:39,Numpad6:39,Numpad2:40,ArrowDown:40,Select:41,Open:43,PrintScreen:44,Insert:45,Numpad0:45,Delete:46,NumpadDecimal:46,Digit0:48,Digit1:49,Digit2:50,Digit3:51,Digit4:52,Digit5:53,Digit6:54,Digit7:55,Digit8:56,Digit9:57,KeyA:65,KeyB:66,KeyC:67,KeyD:68,KeyE:69,KeyF:70,KeyG:71,KeyH:72,KeyI:73,KeyJ:74,KeyK:75,KeyL:76,KeyM:77,KeyN:78,KeyO:79,KeyP:80,KeyQ:81,KeyR:82,KeyS:83,KeyT:84,KeyU:85,KeyV:86,KeyW:87,KeyX:88,KeyY:89,KeyZ:90,MetaLeft:91,MetaRight:92,ContextMenu:93,NumpadMultiply:106,NumpadAdd:107,NumpadSubtract:109,NumpadDivide:111,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,F13:124,F14:125,F15:126,F16:127,F17:128,F18:129,F19:130,F20:131,F21:132,F22:133,F23:134,F24:135,NumLock:144,ScrollLock:145,AudioVolumeMute:173,AudioVolumeDown:174,AudioVolumeUp:175,MediaTrackNext:176,MediaTrackPrevious:177,MediaStop:178,MediaPlayPause:179,Semicolon:186,Equal:187,NumpadEqual:187,Comma:188,Minus:189,Period:190,Slash:191,Backquote:192,BracketLeft:219,Backslash:220,BracketRight:221,Quote:222,AltGraph:225,Props:247,Cancel:3,Clear:12,Shift:16,Control:17,Alt:18,Accept:30,ModeChange:31," ":32,Print:42,Execute:43,"\\u0000":46,a:65,b:66,c:67,d:68,e:69,f:70,g:71,h:72,i:73,j:74,k:75,l:76,m:77,n:78,o:79,p:80,q:81,r:82,s:83,t:84,u:85,v:86,w:87,x:88,y:89,z:90,Meta:91,"*":106,"+":107,"-":109,"/":111,";":186,"=":187,",":188,".":190,"`":192,"[":219,"\\\\":220,"]":221,"'":222,Attn:246,CrSel:247,ExSel:248,EraseEof:249,Play:250,ZoomOut:251,")":48,"!":49,"@":50,"#":51,$:52,"%":53,"^":54,"&":55,"(":57,A:65,B:66,C:67,D:68,E:69,F:70,G:71,H:72,I:73,J:74,K:75,L:76,M:77,N:78,O:79,P:80,Q:81,R:82,S:83,T:84,U:85,V:86,W:87,X:88,Y:89,Z:90,":":186,"<":188,_:189,">":190,"?":191,"~":192,"{":219,"|":220,"}":221,'"':222,Camera:44,EndCall:95,VolumeDown:182,VolumeUp:183},Object.defineProperty(tt,"__esModule",{value:!0}),tt.ActionDispatcher=void 0;const st=g,ot=J,it=at,ct=rt,dt=nt,ut=(e=>{const t=e.getClientRects()[0],a=Math.max(0,Math.min(t.x,t.x+t.width)),r=Math.min(window.innerWidth,Math.max(t.x,t.x+t.width)),n=Math.max(0,Math.min(t.y,t.y+t.height));return[a+(r-a>>1),n+(Math.min(window.innerHeight,Math.max(t.y,t.y+t.height))-n>>1)]}).toString(),lt=(()=>navigator.platform.toLowerCase().includes("mac")).toString();tt.ActionDispatcher=class{static isMacOS=async e=>{const t=await(await e.getOrCreateSandbox(void 0)).callFunction(lt,{type:"undefined"},[],!1,"none",{});return(0,ot.assert)("exception"!==t.type),(0,ot.assert)("boolean"===t.result.type),t.result.value};#Qe=0;#et=0;#tt;#Se;#at;constructor(e,t,a){this.#tt=e,this.#Se=t,this.#at=a}async dispatchActions(e){await this.#tt.queue.run((async()=>{for(const t of e)await this.dispatchTickActions(t)}))}async dispatchTickActions(e){this.#Qe=performance.now(),this.#et=0;for(const{action:t}of e)"duration"in t&&void 0!==t.duration&&(this.#et=Math.max(this.#et,t.duration));const t=[new Promise((e=>setTimeout(e,this.#et)))];for(const a of e)t.push(this.#rt(a));await Promise.all(t)}async#rt({id:e,action:t}){const a=this.#tt.get(e),r=this.#tt.getGlobalKeyState();switch(t.type){case"keyDown":await this.#nt(a,t),this.#tt.cancelList.push({id:e,action:{...t,type:"keyUp"}});break;case"keyUp":await this.#st(a,t);break;case"pause":break;case"pointerDown":await this.#ot(a,r,t),this.#tt.cancelList.push({id:e,action:{...t,type:"pointerUp"}});break;case"pointerMove":await this.#it(a,r,t);break;case"pointerUp":await this.#ct(a,r,t);break;case"scroll":await this.#dt(a,r,t)}}#ot(e,t,a){const{button:r}=a;if(e.pressed.has(r))return;e.pressed.add(r);const{x:n,y:s,subtype:o}=e,{width:i,height:c,pressure:d,twist:u,tangentialPressure:l}=a,{tiltX:h,tiltY:p}=ft(a),{modifiers:m}=t;switch(o){case"mouse":case"pen":return this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mousePressed",x:n,y:s,modifiers:m,button:mt(r),buttons:e.buttons,clickCount:e.setClickCount(r,new it.PointerSource.ClickContext(n,s,performance.now())),pointerType:o,tangentialPressure:l,tiltX:h,tiltY:p,twist:u,force:d});case"touch":return this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchStart",touchPoints:[{x:n,y:s,...gt(i??1,c??1),tangentialPressure:l,tiltX:h,tiltY:p,twist:u,force:d,id:e.pointerId}],modifiers:m})}}#ct(e,t,a){const{button:r}=a;if(!e.pressed.has(r))return;e.pressed.delete(r);const{x:n,y:s,subtype:o}=e,{modifiers:i}=t;switch(o){case"mouse":case"pen":return this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseReleased",x:n,y:s,modifiers:i,button:mt(r),buttons:e.buttons,clickCount:e.getClickCount(r),pointerType:o});case"touch":return this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchEnd",touchPoints:[{x:n,y:s,id:e.pointerId}],modifiers:i})}}async#it(e,t,a){const{x:r,y:n,subtype:s}=e,{width:o,height:i,pressure:c,twist:d,tangentialPressure:u,x:l,y:h,origin:p="viewport",duration:m=this.#et}=a,{tiltX:f,tiltY:g}=ft(a),{targetX:y,targetY:S}=await this.#ut(p,l,h,r,n);if(y<0||S<0)throw new st.MoveTargetOutOfBoundsException(`Cannot move beyond viewport (x: ${y}, y: ${S})`);let v;do{const a=m>0?(performance.now()-this.#Qe)/m:1;let l,h;if(v=a>=1,v?(l=y,h=S):(l=Math.round(a*(y-r)+r),h=Math.round(a*(S-n)+n)),e.x!==l||e.y!==h){const{modifiers:a}=t;switch(s){case"mouse":await this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseMoved",x:l,y:h,modifiers:a,clickCount:0,button:mt(e.pressed.values().next().value??5),buttons:e.buttons,pointerType:s,tangentialPressure:u,tiltX:f,tiltY:g,twist:d,force:c});break;case"pen":0!==e.pressed.size&&await this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseMoved",x:l,y:h,modifiers:a,clickCount:0,button:mt(e.pressed.values().next().value??5),buttons:e.buttons,pointerType:s,tangentialPressure:u,tiltX:f,tiltY:g,twist:d,force:c});break;case"touch":0!==e.pressed.size&&await this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchTouchEvent",{type:"touchMove",touchPoints:[{x:l,y:h,...gt(o??1,i??1),tangentialPressure:u,tiltX:f,tiltY:g,twist:d,force:c,id:e.pointerId}],modifiers:a})}e.x=l,e.y=h}}while(!v)}async#ut(e,t,a,r,n){let s,o;switch(e){case"viewport":s=t,o=a;break;case"pointer":s=r+t,o=n+a;break;default:{const{x:r,y:n}=await async function(e,t){const a=await e.getOrCreateSandbox(void 0),r=await a.callFunction(ut,{type:"undefined"},[t],!1,"none",{});if("exception"===r.type)throw new st.NoSuchElementException(`Origin element ${t.sharedId} was not found`);(0,ot.assert)("array"===r.result.type),(0,ot.assert)("number"===r.result.value?.[0]?.type),(0,ot.assert)("number"===r.result.value?.[1]?.type);const{result:{value:[{value:n},{value:s}]}}=r;return{x:n,y:s}}(this.#Se,e.element);s=r+t,o=n+a;break}}return{targetX:s,targetY:o}}async#dt(e,t,a){const{deltaX:r,deltaY:n,x:s,y:o,origin:i="viewport",duration:c=this.#et}=a;if("pointer"===i)throw new st.InvalidArgumentException('"pointer" origin is invalid for scrolling.');const{targetX:d,targetY:u}=await this.#ut(i,s,o,0,0);if(d<0||u<0)throw new st.MoveTargetOutOfBoundsException(`Cannot move beyond viewport (x: ${d}, y: ${u})`);let l,h=0,p=0;do{const e=c>0?(performance.now()-this.#Qe)/c:1;let a,s;if(l=e>=1,l?(a=r-h,s=n-p):(a=Math.round(e*r-h),s=Math.round(e*n-p)),0!==a||0!==s){const{modifiers:e}=t;await this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchMouseEvent",{type:"mouseWheel",deltaX:a,deltaY:s,x:d,y:u,modifiers:e}),h+=a,p+=s}}while(!l)}async#nt(e,t){if([...t.value].length>1)throw new st.InvalidArgumentException(`Invalid key value: ${t.value}`);const a=t.value,r=(0,ct.getNormalizedKey)(a),n=e.pressed.has(r),s=(0,ct.getKeyCode)(a),o=(0,ct.getKeyLocation)(a);switch(r){case"Alt":e.alt=!0;break;case"Shift":e.shift=!0;break;case"Control":e.ctrl=!0;break;case"Meta":e.meta=!0}e.pressed.add(r);const{modifiers:i}=e,c=ht(r,e),d=pt(s??"",e)??c;let u;if(this.#at&&e.meta)switch(s){case"KeyA":u="SelectAll";break;case"KeyC":u="Copy";break;case"KeyV":u=e.shift?"PasteAndMatchStyle":"Paste";break;case"KeyX":u="Cut";break;case"KeyZ":u=e.shift?"Redo":"Undo"}const l=[this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchKeyEvent",{type:d?"keyDown":"rawKeyDown",windowsVirtualKeyCode:dt.KeyToKeyCode[r],key:r,code:s,text:d,unmodifiedText:c,autoRepeat:n,isSystemKey:e.alt||void 0,location:o<3?o:void 0,isKeypad:3===o,modifiers:i,commands:u?[u]:void 0})];"Escape"===r&&(e.alt||(!this.#at||e.ctrl||e.meta)&&this.#at||l.push(this.#Se.cdpTarget.cdpClient.sendCommand("Input.cancelDragging"))),await Promise.all(l)}#st(e,t){if([...t.value].length>1)throw new st.InvalidArgumentException(`Invalid key value: ${t.value}`);const a=t.value,r=(0,ct.getNormalizedKey)(a);if(!e.pressed.has(r))return;const n=(0,ct.getKeyCode)(a),s=(0,ct.getKeyLocation)(a);switch(r){case"Alt":e.alt=!1;break;case"Shift":e.shift=!1;break;case"Control":e.ctrl=!1;break;case"Meta":e.meta=!1}e.pressed.delete(r);const{modifiers:o}=e,i=ht(r,e),c=pt(n??"",e)??i;return this.#Se.cdpTarget.cdpClient.sendCommand("Input.dispatchKeyEvent",{type:"keyUp",windowsVirtualKeyCode:dt.KeyToKeyCode[r],key:r,code:n,text:c,unmodifiedText:i,location:s<3?s:void 0,isSystemKey:e.alt||void 0,isKeypad:3===s,modifiers:o})}};const ht=(e,t)=>"Enter"===e?"\r":1===[...e].length?t.shift?e.toLocaleUpperCase("en-US"):e:void 0,pt=(e,t)=>{if(t.ctrl){switch(e){case"Digit2":if(t.shift)return"\0";break;case"KeyA":return"\x01";case"KeyB":return"\x02";case"KeyC":return"\x03";case"KeyD":return"\x04";case"KeyE":return"\x05";case"KeyF":return"\x06";case"KeyG":return"\x07";case"KeyH":return"\b";case"KeyI":return"\t";case"KeyJ":return"\n";case"KeyK":return"\v";case"KeyL":return"\f";case"KeyM":return"\r";case"KeyN":return"\x0e";case"KeyO":return"\x0f";case"KeyP":return"\x10";case"KeyQ":return"\x11";case"KeyR":return"\x12";case"KeyS":return"\x13";case"KeyT":return"\x14";case"KeyU":return"\x15";case"KeyV":return"\x16";case"KeyW":return"\x17";case"KeyX":return"\x18";case"KeyY":return"\x19";case"KeyZ":return"\x1a";case"BracketLeft":return"\x1b";case"Backslash":return"\x1c";case"BracketRight":return"\x1d";case"Digit6":if(t.shift)return"\x1e";break;case"Minus":return"\x1f"}return""}if(t.alt)return""};function mt(e){switch(e){case 0:return"left";case 1:return"middle";case 2:return"right";case 3:return"back";case 4:return"forward";default:return"none"}}function ft(e){const t=e.altitudeAngle??0,a=e.azimuthAngle??0;let r=0,n=0;if(0===t&&(0!==a&&a!==2*Math.PI||(r=Math.PI/2),a===Math.PI/2&&(n=Math.PI/2),a===Math.PI&&(r=-Math.PI/2),a===3*Math.PI/2&&(n=-Math.PI/2),a>0&&a<Math.PI/2&&(r=Math.PI/2,n=Math.PI/2),a>Math.PI/2&&a<Math.PI&&(r=-Math.PI/2,n=Math.PI/2),a>Math.PI&&a<3*Math.PI/2&&(r=-Math.PI/2,n=-Math.PI/2),a>3*Math.PI/2&&a<2*Math.PI&&(r=Math.PI/2,n=-Math.PI/2)),0!==t){const e=Math.tan(t);r=Math.atan(Math.cos(a)/e),n=Math.atan(Math.sin(a)/e)}const s=180/Math.PI;return{tiltX:Math.round(r*s),tiltY:Math.round(n*s)}}function gt(e,t){return{radiusX:e?e/2:.5,radiusY:t?t/2:.5}}var yt={},St={},vt={};Object.defineProperty(vt,"__esModule",{value:!0}),vt.Mutex=void 0;vt.Mutex=class{#lt=!1;#ht=[];acquire(){const e={resolved:!1};return this.#lt?new Promise((t=>{this.#ht.push((()=>t(this.#pt.bind(this,e))))})):(this.#lt=!0,Promise.resolve(this.#pt.bind(this,e)))}#pt(e){if(e.resolved)throw new Error("Cannot release more than once.");e.resolved=!0;const t=this.#ht.shift();t?t():this.#lt=!1}async run(e){const t=await this.acquire();try{return await e()}finally{t()}}},Object.defineProperty(St,"__esModule",{value:!0}),St.InputState=void 0;const wt=g,Ct=vt,bt=at;St.InputState=class{cancelList=[];#mt=new Map;#ft=new Ct.Mutex;getOrCreate(e,t,a){let r=this.#mt.get(e);if(!r){switch(t){case"none":r=new bt.NoneSource;break;case"key":r=new bt.KeySource;break;case"pointer":{let e="mouse"===a?0:2;const t=new Set;for(const[,e]of this.#mt)"pointer"===e.type&&t.add(e.pointerId);for(;t.has(e);)++e;r=new bt.PointerSource(e,a);break}case"wheel":r=new bt.WheelSource;break;default:throw new wt.InvalidArgumentException(`Expected "none", "key", "pointer", or "wheel". Found unknown source type ${t}.`)}return this.#mt.set(e,r),r}if(r.type!==t)throw new wt.InvalidArgumentException(`Input source type of ${e} is ${r.type}, but received ${t}.`);return r}get(e){const t=this.#mt.get(e);if(!t)throw new wt.UnknownErrorException("Internal error.");return t}getGlobalKeyState(){const e=new bt.KeySource;for(const[,t]of this.#mt)if("key"===t.type){for(const a of t.pressed)e.pressed.add(a);e.alt||=t.alt,e.ctrl||=t.ctrl,e.meta||=t.meta,e.shift||=t.shift}return e}get queue(){return this.#ft}},Object.defineProperty(yt,"__esModule",{value:!0}),yt.InputStateManager=void 0;const xt=J,Pt=St;class It extends WeakMap{get(e){return(0,xt.assert)(e.isTopLevelContext()),this.has(e)||this.set(e,new Pt.InputState),super.get(e)}}yt.InputStateManager=It,Object.defineProperty(et,"__esModule",{value:!0}),et.InputProcessor=void 0;const kt=g,Rt=J,Et=tt,_t=yt;et.InputProcessor=class{#i;#C;#gt=new _t.InputStateManager;constructor(e,t){this.#i=e,this.#C=t}async performActions(e){const t=this.#i.getContext(e.context),a=this.#gt.get(t.top),r=this.#yt(e,a),n=new Et.ActionDispatcher(a,t,await Et.ActionDispatcher.isMacOS(t).catch((()=>!1)));return await n.dispatchActions(r),{}}async releaseActions(e){const t=this.#i.getContext(e.context),a=t.top,r=this.#gt.get(a),n=new Et.ActionDispatcher(r,t,await Et.ActionDispatcher.isMacOS(t).catch((()=>!1)));return await n.dispatchTickActions(r.cancelList.reverse()),this.#gt.delete(a),{}}async setFiles(e){const t=this.#C.findRealm({browsingContextId:e.context});if(void 0===t)throw new kt.NoSuchFrameException(`Could not find browsingContext ${e.context}`);let a;try{const r=await t.callFunction(String((function(){return this instanceof HTMLInputElement&&"file"===this.type&&!this.disabled})),e.element,[],!1,"none",{},!1);(0,Rt.assert)("success"===r.type),(0,Rt.assert)("boolean"===r.result.type),a=r.result.value}catch{throw new kt.NoSuchElementException(`Could not find element ${e.element.sharedId}`)}if(!a)throw new kt.UnableToSetFileInputException(`Element ${e.element.sharedId} is not a mutable file input.`);const r=[];for(let a=0;a<e.files.length;++a){const a=await t.callFunction(String((function(e){return this.files?this.files.item(e):null})),e.element,[{type:"number",value:0}],!1,"root",{},!1);if((0,Rt.assert)("success"===a.type),"object"!==a.result.type)break;const{handle:n}=a.result;(0,Rt.assert)(void 0!==n);const{path:s}=await t.cdpClient.sendCommand("DOM.getFileInfo",{objectId:n});r.push(s),t.disown(n).catch(void 0)}r.sort();const n=[...e.files].sort();if(r.length!==e.files.length||n.some(((e,t)=>r[t]!==e))){const{objectId:a}=await t.deserializeForCdp(e.element);(0,Rt.assert)(void 0!==a),await t.cdpClient.sendCommand("DOM.setFileInputFiles",{files:e.files,objectId:a})}else await t.callFunction(String((function(){this.dispatchEvent(new Event("cancel",{bubbles:!0}))})),e.element,[],!1,"none",{},!1);return{}}#yt(e,t){const a=[];for(const r of e.actions){switch(r.type){case"pointer":{r.parameters??={pointerType:"mouse"},r.parameters.pointerType??="mouse";const e=t.getOrCreate(r.id,"pointer",r.parameters.pointerType);if(e.subtype!==r.parameters.pointerType)throw new kt.InvalidArgumentException(`Expected input source ${r.id} to be ${e.subtype}; got ${r.parameters.pointerType}.`);break}default:t.getOrCreate(r.id,r.type)}const e=r.actions.map((e=>({id:r.id,action:e})));for(let t=0;t<e.length;t++)a.length===t&&a.push([]),a[t].push(e[t])}return a}};var Tt={},Nt={},jt={},Ot=Object.defineProperty,Mt=Object.getOwnPropertyDescriptor,At=Object.getOwnPropertyNames,Bt=Object.prototype.hasOwnProperty,zt={};((e,t)=>{for(var a in t)Ot(e,a,{get:t[a],enumerable:!0})})(zt,{URLPattern:()=>xa});var Dt,Lt=(Dt=zt,((e,t,a,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of At(t))!Bt.call(e,n)&&n!==a&&Ot(e,n,{get:()=>t[n],enumerable:!(r=Mt(t,n))||r.enumerable});return e})(Ot({},"__esModule",{value:!0}),Dt)),Zt=class{type=3;name="";prefix="";value="";suffix="";modifier=3;constructor(e,t,a,r,n,s){this.type=e,this.name=t,this.prefix=a,this.value=r,this.suffix=n,this.modifier=s}hasCustomName(){return""!==this.name&&"number"!=typeof this.name}},Ut=/[$_\p{ID_Start}]/u,Ft=/[$_\u200C\u200D\p{ID_Continue}]/u,qt=".*";function Vt(e,t){return(t?/^[\x00-\xFF]*$/:/^[\x00-\x7F]*$/).test(e)}function $t(e,t=!1){let a=[],r=0;for(;r<e.length;){let n=e[r],s=function(n){if(!t)throw new TypeError(n);a.push({type:"INVALID_CHAR",index:r,value:e[r++]})};if("*"!==n)if("+"!==n&&"?"!==n)if("\\"!==n)if("{"!==n)if("}"!==n)if(":"!==n)if("("!==n)a.push({type:"CHAR",index:r,value:e[r++]});else{let t=1,n="",o=r+1,i=!1;if("?"===e[o]){s(`Pattern cannot start with "?" at ${o}`);continue}for(;o<e.length;){if(!Vt(e[o],!1)){s(`Invalid character '${e[o]}' at ${o}.`),i=!0;break}if("\\"!==e[o]){if(")"===e[o]){if(t--,0===t){o++;break}}else if("("===e[o]&&(t++,"?"!==e[o+1])){s(`Capturing groups are not allowed at ${o}`),i=!0;break}n+=e[o++]}else n+=e[o++]+e[o++]}if(i)continue;if(t){s(`Unbalanced pattern at ${r}`);continue}if(!n){s(`Missing pattern at ${r}`);continue}a.push({type:"REGEX",index:r,value:n}),r=o}else{let t="",n=r+1;for(;n<e.length;){let a=e.substr(n,1);if(!(n===r+1&&Ut.test(a)||n!==r+1&&Ft.test(a)))break;t+=e[n++]}if(!t){s(`Missing parameter name at ${r}`);continue}a.push({type:"NAME",index:r,value:t}),r=n}else a.push({type:"CLOSE",index:r,value:e[r++]});else a.push({type:"OPEN",index:r,value:e[r++]});else a.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});else a.push({type:"OTHER_MODIFIER",index:r,value:e[r++]});else a.push({type:"ASTERISK",index:r,value:e[r++]})}return a.push({type:"END",index:r,value:""}),a}function Kt(e,t={}){let a=$t(e);t.delimiter??="/#?",t.prefixes??="./";let r=`[^${Ht(t.delimiter)}]+?`,n=[],s=0,o=0,i=new Set,c=e=>{if(o<a.length&&a[o].type===e)return a[o++].value},d=()=>c("OTHER_MODIFIER")??c("ASTERISK"),u=e=>{let t=c(e);if(void 0!==t)return t;let{type:r,index:n}=a[o];throw new TypeError(`Unexpected ${r} at ${n}, expected ${e}`)},l=()=>{let e,t="";for(;e=c("CHAR")??c("ESCAPED_CHAR");)t+=e;return t},h=t.encodePart||(e=>e),p="",m=e=>{p+=e},f=()=>{p.length&&(n.push(new Zt(3,"","",h(p),"",3)),p="")},g=(e,t,a,o,c)=>{let d,u=3;switch(c){case"?":u=1;break;case"*":u=0;break;case"+":u=2}if(!t&&!a&&3===u)return void m(e);if(f(),!t&&!a){if(!e)return;return void n.push(new Zt(3,"","",h(e),"",u))}d=a?"*"===a?qt:a:r;let l,p=2;if(d===r?(p=1,d=""):d===qt&&(p=0,d=""),t?l=t:a&&(l=s++),i.has(l))throw new TypeError(`Duplicate name '${l}'.`);i.add(l),n.push(new Zt(p,l,h(e),d,h(o),u))};for(;o<a.length;){let e=c("CHAR"),a=c("NAME"),r=c("REGEX");if(!a&&!r&&(r=c("ASTERISK")),a||r){let n=e??"";-1===t.prefixes.indexOf(n)&&(m(n),n=""),f(),g(n,a,r,"",d());continue}let n=e??c("ESCAPED_CHAR");if(n)m(n);else if(c("OPEN")){let e=l(),t=c("NAME"),a=c("REGEX");!t&&!a&&(a=c("ASTERISK"));let r=l();u("CLOSE"),g(e,t,a,r,d())}else f(),u("END")}return n}function Ht(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}function Wt(e){return e&&e.ignoreCase?"ui":"u"}function Jt(e){switch(e){case 0:return"*";case 1:return"?";case 2:return"+";case 3:return""}}function Gt(e,t,a={}){a.delimiter??="/#?",a.prefixes??="./",a.sensitive??=!1,a.strict??=!1,a.end??=!0,a.start??=!0,a.endsWith="";let r=a.start?"^":"";for(let n of e){if(3===n.type){3===n.modifier?r+=Ht(n.value):r+=`(?:${Ht(n.value)})${Jt(n.modifier)}`;continue}t&&t.push(n.name);let e=`[^${Ht(a.delimiter)}]+?`,s=n.value;(1===n.type?s=e:0===n.type&&(s=qt),n.prefix.length||n.suffix.length)?3!==n.modifier&&1!==n.modifier?(r+=`(?:${Ht(n.prefix)}`,r+=`((?:${s})(?:`,r+=Ht(n.suffix),r+=Ht(n.prefix),r+=`(?:${s}))*)${Ht(n.suffix)})`,0===n.modifier&&(r+="?")):(r+=`(?:${Ht(n.prefix)}(${s})${Ht(n.suffix)})`,r+=Jt(n.modifier)):3===n.modifier||1===n.modifier?r+=`(${s})${Jt(n.modifier)}`:r+=`((?:${s})${Jt(n.modifier)})`}let n=`[${Ht(a.endsWith)}]|$`,s=`[${Ht(a.delimiter)}]`;if(a.end)return a.strict||(r+=`${s}?`),a.endsWith.length?r+=`(?=${n})`:r+="$",new RegExp(r,Wt(a));a.strict||(r+=`(?:${s}(?=${n}))?`);let o=!1;if(e.length){let t=e[e.length-1];3===t.type&&3===t.modifier&&(o=a.delimiter.indexOf(t)>-1)}return o||(r+=`(?=${s}|${n})`),new RegExp(r,Wt(a))}var Xt={delimiter:"",prefixes:"",sensitive:!0,strict:!0},Yt={delimiter:".",prefixes:"",sensitive:!0,strict:!0},Qt={delimiter:"/",prefixes:"/",sensitive:!0,strict:!0};function ea(e,t){return e.startsWith(t)?e.substring(t.length,e.length):e}function ta(e){return!(!e||e.length<2)&&("["===e[0]||("\\"===e[0]||"{"===e[0])&&"["===e[1])}var aa=["ftp","file","http","https","ws","wss"];function ra(e){if(!e)return!0;for(let t of aa)if(e.test(t))return!0;return!1}function na(e){switch(e){case"ws":case"http":return"80";case"wws":case"https":return"443";case"ftp":return"21";default:return""}}function sa(e){if(""===e)return e;if(/^[-+.A-Za-z0-9]*$/.test(e))return e.toLowerCase();throw new TypeError(`Invalid protocol '${e}'.`)}function oa(e){if(""===e)return e;let t=new URL("https://example.com");return t.username=e,t.username}function ia(e){if(""===e)return e;let t=new URL("https://example.com");return t.password=e,t.password}function ca(e){if(""===e)return e;if(/[\t\n\r #%/:<>?@[\]^\\|]/g.test(e))throw new TypeError(`Invalid hostname '${e}'`);let t=new URL("https://example.com");return t.hostname=e,t.hostname}function da(e){if(""===e)return e;if(/[^0-9a-fA-F[\]:]/g.test(e))throw new TypeError(`Invalid IPv6 hostname '${e}'`);return e.toLowerCase()}function ua(e){if(""===e||/^[0-9]*$/.test(e)&&parseInt(e)<=65535)return e;throw new TypeError(`Invalid port '${e}'.`)}function la(e){if(""===e)return e;let t=new URL("https://example.com");return t.pathname="/"!==e[0]?"/-"+e:e,"/"!==e[0]?t.pathname.substring(2,t.pathname.length):t.pathname}function ha(e){return""===e?e:new URL(`data:${e}`).pathname}function pa(e){if(""===e)return e;let t=new URL("https://example.com");return t.search=e,t.search.substring(1,t.search.length)}function ma(e){if(""===e)return e;let t=new URL("https://example.com");return t.hash=e,t.hash.substring(1,t.hash.length)}var fa=class{#St;#vt=[];#wt={};#Ct=0;#bt=1;#xt=0;#Pt=0;#It=0;#kt=0;#Rt=!1;constructor(e){this.#St=e}get result(){return this.#wt}parse(){for(this.#vt=$t(this.#St,!0);this.#Ct<this.#vt.length;this.#Ct+=this.#bt){if(this.#bt=1,"END"===this.#vt[this.#Ct].type){if(0===this.#Pt){this.#Et(),this.#_t()?this.#Tt(9,1):this.#Nt()?this.#Tt(8,1):this.#Tt(7,0);continue}if(2===this.#Pt){this.#jt(5);continue}this.#Tt(10,0);break}if(this.#It>0){if(!this.#Ot())continue;this.#It-=1}if(this.#Mt())this.#It+=1;else switch(this.#Pt){case 0:this.#At()&&this.#jt(1);break;case 1:if(this.#At()){this.#Bt();let e=7,t=1;this.#zt()?(e=2,t=3):this.#Rt&&(e=2),this.#Tt(e,t)}break;case 2:this.#Dt()?this.#jt(3):(this.#Je()||this.#Nt()||this.#_t())&&this.#jt(5);break;case 3:this.#Lt()?this.#Tt(4,1):this.#Dt()&&this.#Tt(5,1);break;case 4:this.#Dt()&&this.#Tt(5,1);break;case 5:this.#Ge()?this.#kt+=1:this.#Zt()&&(this.#kt-=1),this.#Ut()&&!this.#kt?this.#Tt(6,1):this.#Je()?this.#Tt(7,0):this.#Nt()?this.#Tt(8,1):this.#_t()&&this.#Tt(9,1);break;case 6:this.#Je()?this.#Tt(7,0):this.#Nt()?this.#Tt(8,1):this.#_t()&&this.#Tt(9,1);break;case 7:this.#Nt()?this.#Tt(8,1):this.#_t()&&this.#Tt(9,1);break;case 8:this.#_t()&&this.#Tt(9,1)}}void 0!==this.#wt.hostname&&void 0===this.#wt.port&&(this.#wt.port="")}#Tt(e,t){switch(this.#Pt){case 0:case 2:break;case 1:this.#wt.protocol=this.#Ft();break;case 3:this.#wt.username=this.#Ft();break;case 4:this.#wt.password=this.#Ft();break;case 5:this.#wt.hostname=this.#Ft();break;case 6:this.#wt.port=this.#Ft();break;case 7:this.#wt.pathname=this.#Ft();break;case 8:this.#wt.search=this.#Ft();break;case 9:this.#wt.hash=this.#Ft()}0!==this.#Pt&&10!==e&&([1,2,3,4].includes(this.#Pt)&&[6,7,8,9].includes(e)&&(this.#wt.hostname??=""),[1,2,3,4,5,6].includes(this.#Pt)&&[8,9].includes(e)&&(this.#wt.pathname??=this.#Rt?"/":""),[1,2,3,4,5,6,7].includes(this.#Pt)&&9===e&&(this.#wt.search??="")),this.#qt(e,t)}#qt(e,t){this.#Pt=e,this.#xt=this.#Ct+t,this.#Ct+=t,this.#bt=0}#Et(){this.#Ct=this.#xt,this.#bt=0}#jt(e){this.#Et(),this.#Pt=e}#Vt(e){return e<0&&(e=this.#vt.length-e),e<this.#vt.length?this.#vt[e]:this.#vt[this.#vt.length-1]}#$t(e,t){let a=this.#Vt(e);return a.value===t&&("CHAR"===a.type||"ESCAPED_CHAR"===a.type||"INVALID_CHAR"===a.type)}#At(){return this.#$t(this.#Ct,":")}#zt(){return this.#$t(this.#Ct+1,"/")&&this.#$t(this.#Ct+2,"/")}#Dt(){return this.#$t(this.#Ct,"@")}#Lt(){return this.#$t(this.#Ct,":")}#Ut(){return this.#$t(this.#Ct,":")}#Je(){return this.#$t(this.#Ct,"/")}#Nt(){if(this.#$t(this.#Ct,"?"))return!0;if("?"!==this.#vt[this.#Ct].value)return!1;let e=this.#Vt(this.#Ct-1);return"NAME"!==e.type&&"REGEX"!==e.type&&"CLOSE"!==e.type&&"ASTERISK"!==e.type}#_t(){return this.#$t(this.#Ct,"#")}#Mt(){return"OPEN"==this.#vt[this.#Ct].type}#Ot(){return"CLOSE"==this.#vt[this.#Ct].type}#Ge(){return this.#$t(this.#Ct,"[")}#Zt(){return this.#$t(this.#Ct,"]")}#Ft(){let e=this.#vt[this.#Ct],t=this.#Vt(this.#xt).index;return this.#St.substring(t,e.index)}#Bt(){let e={};Object.assign(e,Xt),e.encodePart=sa;let t=function(e,t,a){return Gt(Kt(e,a),t,a)}(this.#Ft(),void 0,e);this.#Rt=ra(t)}},ga=["protocol","username","password","hostname","port","pathname","search","hash"],ya="*";function Sa(e,t){if("string"!=typeof e)throw new TypeError("parameter 1 is not of type 'string'.");let a=new URL(e,t);return{protocol:a.protocol.substring(0,a.protocol.length-1),username:a.username,password:a.password,hostname:a.hostname,port:a.port,pathname:a.pathname,search:""!==a.search?a.search.substring(1,a.search.length):void 0,hash:""!==a.hash?a.hash.substring(1,a.hash.length):void 0}}function va(e,t){return t?Ca(e):e}function wa(e,t,a){let r;if("string"==typeof t.baseURL)try{r=new URL(t.baseURL),void 0===t.protocol&&(e.protocol=va(r.protocol.substring(0,r.protocol.length-1),a)),!a&&void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.username&&(e.username=va(r.username,a)),!a&&void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.username&&void 0===t.password&&(e.password=va(r.password,a)),void 0===t.protocol&&void 0===t.hostname&&(e.hostname=va(r.hostname,a)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&(e.port=va(r.port,a)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&(e.pathname=va(r.pathname,a)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&void 0===t.search&&(e.search=va(r.search.substring(1,r.search.length),a)),void 0===t.protocol&&void 0===t.hostname&&void 0===t.port&&void 0===t.pathname&&void 0===t.search&&void 0===t.hash&&(e.hash=va(r.hash.substring(1,r.hash.length),a))}catch{throw new TypeError(`invalid baseURL '${t.baseURL}'.`)}if("string"==typeof t.protocol&&(e.protocol=function(e,t){return e=function(e,t){return e.endsWith(t)?e.substr(0,e.length-t.length):e}(e,":"),t||""===e?e:sa(e)}(t.protocol,a)),"string"==typeof t.username&&(e.username=function(e,t){if(t||""===e)return e;let a=new URL("https://example.com");return a.username=e,a.username}(t.username,a)),"string"==typeof t.password&&(e.password=function(e,t){if(t||""===e)return e;let a=new URL("https://example.com");return a.password=e,a.password}(t.password,a)),"string"==typeof t.hostname&&(e.hostname=function(e,t){return t||""===e?e:ta(e)?da(e):ca(e)}(t.hostname,a)),"string"==typeof t.port&&(e.port=function(e,t,a){return na(t)===e&&(e=""),a||""===e?e:ua(e)}(t.port,e.protocol,a)),"string"==typeof t.pathname){if(e.pathname=t.pathname,r&&!function(e,t){return!(!e.length||"/"!==e[0]&&(!t||e.length<2||"\\"!=e[0]&&"{"!=e[0]||"/"!=e[1]))}(e.pathname,a)){let t=r.pathname.lastIndexOf("/");t>=0&&(e.pathname=va(r.pathname.substring(0,t+1),a)+e.pathname)}e.pathname=function(e,t,a){if(a||""===e)return e;if(t&&!aa.includes(t))return new URL(`${t}:${e}`).pathname;let r="/"==e[0];return e=new URL(r?e:"/-"+e,"https://example.com").pathname,r||(e=e.substring(2,e.length)),e}(e.pathname,e.protocol,a)}return"string"==typeof t.search&&(e.search=function(e,t){if(e=ea(e,"?"),t||""===e)return e;let a=new URL("https://example.com");return a.search=e,a.search?a.search.substring(1,a.search.length):""}(t.search,a)),"string"==typeof t.hash&&(e.hash=function(e,t){if(e=ea(e,"#"),t||""===e)return e;let a=new URL("https://example.com");return a.hash=e,a.hash?a.hash.substring(1,a.hash.length):""}(t.hash,a)),e}function Ca(e){return e.replace(/([+*?:{}()\\])/g,"\\$1")}function ba(e,t){t.delimiter??="/#?",t.prefixes??="./",t.sensitive??=!1,t.strict??=!1,t.end??=!0,t.start??=!0,t.endsWith="";let a=`[^${function(e){return e.replace(/([.+*?^${}()[\]|/\\])/g,"\\$1")}(t.delimiter)}]+?`,r=/[$_\u200C\u200D\p{ID_Continue}]/u,n="";for(let s=0;s<e.length;++s){let o=e[s];if(3===o.type){if(3===o.modifier){n+=Ca(o.value);continue}n+=`{${Ca(o.value)}}${Jt(o.modifier)}`;continue}let i=o.hasCustomName(),c=!!o.suffix.length||!!o.prefix.length&&(1!==o.prefix.length||!t.prefixes.includes(o.prefix)),d=s>0?e[s-1]:null,u=s<e.length-1?e[s+1]:null;if(!c&&i&&1===o.type&&3===o.modifier&&u&&!u.prefix.length&&!u.suffix.length)if(3===u.type){let e=u.value.length>0?u.value[0]:"";c=r.test(e)}else c=!u.hasCustomName();if(!c&&!o.prefix.length&&d&&3===d.type){let e=d.value[d.value.length-1];c=t.prefixes.includes(e)}c&&(n+="{"),n+=Ca(o.prefix),i&&(n+=`:${o.name}`),2===o.type?n+=`(${o.value})`:1===o.type?i||(n+=`(${a})`):0===o.type&&(i||d&&3!==d.type&&3===d.modifier&&!c&&""===o.prefix?n+="(.*)":n+="*"),1===o.type&&i&&o.suffix.length&&r.test(o.suffix[0])&&(n+="\\"),n+=Ca(o.suffix),c&&(n+="}"),3!==o.modifier&&(n+=Jt(o.modifier))}return n}var xa=class{#St;#vt={};#wt={};#Ct={};#bt={};#xt=!1;constructor(e={},t,a){try{let r;if("string"==typeof t?r=t:a=t,"string"==typeof e){let t=new fa(e);if(t.parse(),e=t.result,void 0===r&&"string"!=typeof e.protocol)throw new TypeError("A base URL must be provided for a relative constructor string.");e.baseURL=r}else{if(!e||"object"!=typeof e)throw new TypeError("parameter 1 is not of type 'string' and cannot convert to dictionary.");if(r)throw new TypeError("parameter 1 is not of type 'string'.")}typeof a>"u"&&(a={ignoreCase:!1});let n,s={ignoreCase:!0===a.ignoreCase},o={pathname:ya,protocol:ya,username:ya,password:ya,hostname:ya,port:ya,search:ya,hash:ya};for(n of(this.#St=wa(o,e,!0),na(this.#St.protocol)===this.#St.port&&(this.#St.port=""),ga)){if(!(n in this.#St))continue;let e={},t=this.#St[n];switch(this.#wt[n]=[],n){case"protocol":Object.assign(e,Xt),e.encodePart=sa;break;case"username":Object.assign(e,Xt),e.encodePart=oa;break;case"password":Object.assign(e,Xt),e.encodePart=ia;break;case"hostname":Object.assign(e,Yt),ta(t)?e.encodePart=da:e.encodePart=ca;break;case"port":Object.assign(e,Xt),e.encodePart=ua;break;case"pathname":ra(this.#vt.protocol)?(Object.assign(e,Qt,s),e.encodePart=la):(Object.assign(e,Xt,s),e.encodePart=ha);break;case"search":Object.assign(e,Xt,s),e.encodePart=pa;break;case"hash":Object.assign(e,Xt,s),e.encodePart=ma}try{this.#bt[n]=Kt(t,e),this.#vt[n]=Gt(this.#bt[n],this.#wt[n],e),this.#Ct[n]=ba(this.#bt[n],e),this.#xt=this.#xt||this.#bt[n].some((e=>2===e.type))}catch{throw new TypeError(`invalid ${n} pattern '${this.#St[n]}'.`)}}}catch(e){throw new TypeError(`Failed to construct 'URLPattern': ${e.message}`)}}test(e={},t){let a,r={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if("string"!=typeof e&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return!1;try{r=wa(r,"object"==typeof e?e:Sa(e,t),!1)}catch{return!1}for(a of ga)if(!this.#vt[a].exec(r[a]))return!1;return!0}exec(e={},t){let a={pathname:"",protocol:"",username:"",password:"",hostname:"",port:"",search:"",hash:""};if("string"!=typeof e&&t)throw new TypeError("parameter 1 is not of type 'string'.");if(typeof e>"u")return;try{a=wa(a,"object"==typeof e?e:Sa(e,t),!1)}catch{return null}let r,n={};for(r of(n.inputs=t?[e,t]:[e],ga)){let e=this.#vt[r].exec(a[r]);if(!e)return null;let t={};for(let[a,n]of this.#wt[r].entries())if("string"==typeof n||"number"==typeof n){let r=e[a+1];t[n]=r}n[r]={input:a[r]??"",groups:t}}return n}static compareComponent(e,t,a){let r=(e,t)=>{for(let a of["type","modifier","prefix","value","suffix"]){if(e[a]<t[a])return-1;if(e[a]!==t[a])return 1}return 0},n=new Zt(3,"","","","",3),s=new Zt(0,"","","","",3),o=(e,t)=>{let a=0;for(;a<Math.min(e.length,t.length);++a){let n=r(e[a],t[a]);if(n)return n}return e.length===t.length?0:r(e[a]??n,t[a]??n)};return t.#Ct[e]||a.#Ct[e]?t.#Ct[e]&&!a.#Ct[e]?o(t.#bt[e],[s]):!t.#Ct[e]&&a.#Ct[e]?o([s],a.#bt[e]):o(t.#bt[e],a.#bt[e]):0}get protocol(){return this.#Ct.protocol}get username(){return this.#Ct.username}get password(){return this.#Ct.password}get hostname(){return this.#Ct.hostname}get port(){return this.#Ct.port}get pathname(){return this.#Ct.pathname}get search(){return this.#Ct.search}get hash(){return this.#Ct.hash}get hasRegExpGroups(){return this.#xt}};const{URLPattern:Pa}=Lt;var Ia={URLPattern:Pa};globalThis.URLPattern||(globalThis.URLPattern=Pa),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.URLPattern=void 0;const t=Ia;Object.defineProperty(e,"URLPattern",{enumerable:!0,get:function(){return t.URLPattern}}),"URLPattern"in globalThis&&(t.URLPattern=globalThis.URLPattern)}(jt),Object.defineProperty(Nt,"__esModule",{value:!0}),Nt.NetworkStorage=void 0;const ka=g,Ra=jt,Ea=A;class _a{#Kt=new Map;#Ht=new Map;#Wt=new Map;disposeRequestMap(){for(const e of this.#Kt.values())e.dispose();this.#Kt.clear()}addIntercept(e){const t=(0,Ea.uuidv4)();return this.#Ht.set(t,e),t}removeIntercept(e){if(!this.#Ht.has(e))throw new ka.NoSuchInterceptException(`Intercept '${e}' does not exist.`);this.#Ht.delete(e)}hasIntercepts(){return this.#Ht.size>0}getFetchEnableParams(){const e=[];for(const t of this.#Ht.values())for(const a of t.phases){const r=_a.requestStageFromPhase(a);if(0!==t.urlPatterns.length)for(const a of t.urlPatterns){const t=_a.cdpFromSpecUrlPattern(a);e.push({urlPattern:t,requestStage:r})}else e.push({urlPattern:"*",requestStage:r})}return{patterns:e,handleAuthRequests:[...this.#Ht.values()].some((e=>e.phases.includes("authRequired")))}}getRequest(e){return this.#Kt.get(e)}addRequest(e){this.#Kt.set(e.requestId,e)}deleteRequest(e){const t=this.#Kt.get(e);t&&(t.dispose(),this.#Kt.delete(e))}hasNetworkRequests(){return this.#Kt.size>0}hasBlockedRequests(){return this.#Wt.size>0}static cdpFromSpecUrlPattern(e){switch(e.type){case"string":return e.pattern;case"pattern":return _a.buildUrlPatternString(e)}}static buildUrlPatternString({protocol:e,hostname:t,port:a,pathname:r,search:n}){if(!(e||t||a||r||n))return"*";let s="";return e&&(s+=e,e.endsWith(":")||(s+=":"),_a.isSpecialScheme(e)&&(s+="//")),t&&(s+=t),a&&(s+=`:${a}`),r&&(r.startsWith("/")||(s+="/"),s+=r),n&&(n.startsWith("?")||(s+="?"),s+=n),s}static requestStageFromPhase(e){switch(e){case"beforeRequestSent":return"Request";case"responseStarted":case"authRequired":return"Response"}}static isSpecialScheme(e){return["ftp","file","http","https","ws","wss"].includes(e.replace(/:$/,""))}addBlockedRequest(e,t){this.#Wt.set(e,t)}removeBlockedRequest(e){this.#Wt.delete(e)}getBlockedRequest(e){return this.#Wt.get(e)}getNetworkIntercepts(e,t){const a=this.#Kt.get(e);if(!a)return[];const r=[];for(const[e,{phases:n,urlPatterns:s}]of this.#Ht.entries())t&&n.includes(t)&&(0===s.length||s.some((e=>_a.matchUrlPattern(e,a.url))))&&r.push(e);return r}static matchUrlPattern(e,t){switch(e.type){case"string":return e.pattern===t;case"pattern":return null!==new Ra.URLPattern({protocol:e.protocol,hostname:e.hostname,port:e.port,pathname:e.pathname,search:e.search}).exec(t)}}}Nt.NetworkStorage=_a,Object.defineProperty(Tt,"__esModule",{value:!0}),Tt.NetworkProcessor=void 0;const Ta=g,Na=J,ja=Nt,Oa=Me;class Ma{#i;#ie;constructor(e,t){this.#i=e,this.#ie=t}async addIntercept(e){e.phases.includes("authRequired")&&!e.phases.includes("beforeRequestSent")&&e.phases.unshift("beforeRequestSent");const t=e.urlPatterns??[],a=Ma.parseUrlPatterns(t),r=this.#ie.addIntercept({urlPatterns:a,phases:e.phases});return await this.#Jt(),{intercept:r}}async continueRequest(e){const t=e.request,{request:a,phase:r}=this.#Gt(t);if("beforeRequestSent"!==r)throw new Ta.InvalidArgumentException(`Blocked request for network id '${t}' is not in 'BeforeRequestSent' phase`);void 0!==e.url&&Ma.parseUrlString(e.url);const{url:n,method:s,headers:o}=e,i=(0,Oa.cdpFetchHeadersFromBidiNetworkHeaders)(o),c=this.#Xt(t);return await c.continueRequest(a,n,s,i),this.#ie.removeBlockedRequest(t),{}}async continueResponse(e){const t=e.request,{request:a,phase:r}=this.#Gt(t);if("beforeRequestSent"===r)throw new Ta.InvalidArgumentException(`Blocked request for network id '${t}' is in 'BeforeRequestSent' phase`);const{statusCode:n,reasonPhrase:s,headers:o}=e,i=(0,Oa.cdpFetchHeadersFromBidiNetworkHeaders)(o),c=this.#Xt(t);return await c.continueResponse(a,n,s,i),this.#ie.removeBlockedRequest(t),{}}async continueWithAuth(e){const t=e.request,{request:a,phase:r}=this.#Gt(t);if("authRequired"!==r)throw new Ta.InvalidArgumentException(`Blocked request for network id '${t}' is not in 'AuthRequired' phase`);const n=this.#Xt(t);let s,o;if("provideCredentials"===e.action){const{credentials:t}=e;s=t.username,o=t.password,(0,Na.assert)("password"===t.type,`Credentials type ${t.type} must be 'password'`)}const i=(0,Oa.cdpAuthChallengeResponseFromBidiAuthContinueWithAuthAction)(e.action);return await n.continueWithAuth(a,i,s,o),{}}async failRequest({request:e}){const{request:t,phase:a}=this.#Gt(e);if("authRequired"===a)throw new Ta.InvalidArgumentException(`Blocked request for network id '${e}' is in 'AuthRequired' phase`);const r=this.#Xt(e);return await r.failRequest(t,"Failed"),this.#ie.removeBlockedRequest(e),{}}async provideResponse(e){const{statusCode:t,reasonPhrase:a,headers:r,body:n,request:s}=e,{request:o}=this.#Gt(s),i=(0,Oa.cdpFetchHeadersFromBidiNetworkHeaders)(r),c=this.#Xt(s);return await c.provideResponse(o,t??c.statusCode,a,i,n?.value),this.#ie.removeBlockedRequest(s),{}}async removeIntercept(e){return this.#ie.removeIntercept(e.intercept),await this.#Jt(),{}}async#Yt(){await Promise.all(this.#i.getAllContexts().map((async e=>{await e.cdpTarget.fetchEnable()})))}async#Qt(){await Promise.all(this.#i.getAllContexts().map((async e=>{await e.cdpTarget.fetchDisable()})))}async#Jt(){this.#ie.hasIntercepts()||this.#ie.hasBlockedRequests()||this.#ie.hasNetworkRequests()?await this.#Yt():await this.#Qt()}#Gt(e){const t=this.#ie.getBlockedRequest(e);if(!t)throw new Ta.NoSuchRequestException(`No blocked request found for network id '${e}'`);return t}#Xt(e){const t=this.#ie.getRequest(e);if(!t)throw new Ta.NoSuchRequestException(`Network request with ID ${e} doesn't exist`);return t}static parseUrlString(e){try{return new URL(e)}catch(t){throw new Ta.InvalidArgumentException(`Invalid URL '${e}': ${t}`)}}static parseUrlPatterns(e){return e.map((e=>{switch(e.type){case"string":return Ma.parseUrlString(e.pattern),e;case"pattern":if(void 0===e.protocol&&void 0===e.hostname&&void 0===e.port&&void 0===e.pathname&&void 0===e.search)return e;if(""===e.protocol)throw new Ta.InvalidArgumentException("URL pattern must specify a protocol");if(""===e.hostname)throw new Ta.InvalidArgumentException("URL pattern must specify a hostname");if((e.hostname?.length??0)>0){if(e.protocol?.match(/^file/i))throw new Ta.InvalidArgumentException("URL pattern protocol cannot be 'file'");if(e.hostname?.includes(":"))throw new Ta.InvalidArgumentException("URL pattern hostname must not contain a colon")}if(""===e.port)throw new Ta.InvalidArgumentException("URL pattern must specify a port");try{new URL(ja.NetworkStorage.buildUrlPatternString(e))}catch(e){throw new Ta.InvalidArgumentException(`${e}`)}return e}}))}}Tt.NetworkProcessor=Ma;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.PermissionsProcessor=void 0;const Ba=g;Aa.PermissionsProcessor=class{#o;constructor(e){this.#o=e}async setPermissions(e){try{await this.#o.sendCommand("Browser.setPermission",{origin:e.origin,permission:{name:e.descriptor.name},setting:e.state})}catch(e){if("Permission can't be granted to opaque origins."===e.message)return{};throw new Ba.InvalidArgumentException(e.message)}return{}}};var za={};Object.defineProperty(za,"__esModule",{value:!0}),za.PreloadScriptStorage=void 0;za.PreloadScriptStorage=class{#ea=new Set;find(e){return e?[...this.#ea].filter((t=>(void 0===e.id||e.id===t.id)&&(!(void 0!==e.targetId&&!t.targetIds.has(e.targetId))&&(void 0===e.global||!(e.global&&void 0!==t.contexts||!e.global&&void 0===t.contexts))))):[...this.#ea]}add(e){this.#ea.add(e)}remove(e){for(const t of this.find(e))this.#ea.delete(t)}};var Da={},La={};Object.defineProperty(La,"__esModule",{value:!0}),La.PreloadScript=void 0;const Za=A,Ua=B;La.PreloadScript=class{#u=(0,Za.uuidv4)();#ta=[];#aa;#ra=new Set;#na;#sa;#oa;get id(){return this.#u}get targetIds(){return this.#ra}constructor(e,t){this.#na=e.arguments?.map((e=>new Ua.ChannelProxy(e.value,t)))??[],this.#aa=e.functionDeclaration,this.#sa=e.sandbox,this.#oa=e.contexts}get channels(){return this.#na}get contexts(){return this.#oa}#ia(){const e=`[${this.channels.map((e=>e.getEvalInWindowStr())).join(", ")}]`;return`(()=>{(${this.#aa})(...${e})})()`}async initInTargets(e,t){await Promise.all(Array.from(e).map((e=>this.initInTarget(e,t))))}async initInTarget(e,t){const a=await e.cdpClient.sendCommand("Page.addScriptToEvaluateOnNewDocument",{source:this.#ia(),worldName:this.#sa,runImmediately:t});this.#ta.push({target:e,preloadScriptId:a.identifier}),this.#ra.add(e.id)}async remove(){for(const e of this.#ta){const t=e.target,a=e.preloadScriptId;await t.cdpClient.sendCommand("Page.removeScriptToEvaluateOnNewDocument",{identifier:a})}}dispose(e){this.#ta=this.#ta.filter((t=>t.target?.id!==e)),this.#ra.delete(e)}},Object.defineProperty(Da,"__esModule",{value:!0}),Da.ScriptProcessor=void 0;const Fa=g,qa=La;Da.ScriptProcessor=class{#i;#C;#_e;#t;constructor(e,t,a,r){this.#i=e,this.#C=t,this.#_e=a,this.#t=r}async addPreloadScript(e){const t=new Set;if(e.contexts){if(0===e.contexts.length)throw new Fa.InvalidArgumentException("Contexts list is empty.");for(const a of e.contexts){const e=this.#i.getContext(a);if(!e.isTopLevelContext())throw new Fa.InvalidArgumentException(`Non top-level context '${a}' given.`);t.add(e)}}const a=new qa.PreloadScript(e,this.#t);this.#_e.add(a);const r=0===t.size?new Set(this.#i.getTopLevelContexts().map((e=>e.cdpTarget))):new Set([...t.values()].map((e=>e.cdpTarget)));return await a.initInTargets(r,!1),{script:a.id}}async removePreloadScript(e){const t=e.script,a=this.#_e.find({id:t});if(0===a.length)throw new Fa.NoSuchScriptException(`No preload script with BiDi ID '${t}'`);return await Promise.all(a.map((e=>e.remove()))),this.#_e.remove({id:t}),{}}async callFunction(e){const t=await this.#ca(e.target);return await t.callFunction(e.functionDeclaration,e.this??{type:"undefined"},e.arguments??[],e.awaitPromise,e.resultOwnership??"none",e.serializationOptions??{},e.userActivation??!1)}async evaluate(e){const t=await this.#ca(e.target);return await t.evaluate(e.expression,e.awaitPromise,e.resultOwnership??"none",e.serializationOptions??{},e.userActivation??!1)}async disown(e){const t=await this.#ca(e.target);return await Promise.all(e.handles.map((async e=>await t.disown(e)))),{}}getRealms(e){void 0!==e.context&&this.#i.getContext(e.context);return{realms:this.#C.findRealms({browsingContextId:e.context,type:e.type}).map((e=>e.realmInfo))}}async#ca(e){if("realm"in e)return this.#C.getRealm({realmId:e.realm});const t=this.#i.getContext(e.context);return await t.getOrCreateSandbox(e.sandbox)}};var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.SessionProcessor=void 0;Va.SessionProcessor=class{#y;constructor(e){this.#y=e}status(){return{ready:!1,message:"already connected"}}subscribe(e,t=null){return this.#y.subscribe(e.events,e.contexts??[null],t),{}}unsubscribe(e,t=null){return this.#y.unsubscribe(e.events,e.contexts??[null],t),{}}};var $a={};Object.defineProperty($a,"__esModule",{value:!0}),$a.StorageProcessor=void 0;const Ka=g,Ha=J,Wa=l,Ja=Tt,Ga=Me;$a.StorageProcessor=class{#o;#i;#t;constructor(e,t,a){this.#i=t,this.#o=e,this.#t=a}async getCookies(e){const t=this.#da(e.partition);return{cookies:(await this.#o.sendCommand("Storage.getCookies",{browserContextId:t.userContext})).cookies.filter((e=>void 0===t.sourceOrigin||e.partitionKey===t.sourceOrigin)).map((e=>(0,Ga.cdpToBiDiCookie)(e))).filter((t=>this.#ua(t,e.filter))),partitionKey:t}}async setCookie(e){const t=this.#da(e.partition),a=(0,Ga.bidiToCdpCookie)(e,t);try{await this.#o.sendCommand("Storage.setCookies",{cookies:[a],browserContextId:t.userContext})}catch(e){throw this.#t?.(Wa.LogType.debugError,e),new Ka.UnableToSetCookieException(e.toString())}return{partitionKey:t}}#la(e){const t=e.context,a=this.#i.getContext(t);return{userContext:"default"===a.userContext?void 0:a.userContext}}#ha(e){const t=new Map;let a=e.sourceOrigin;if(void 0!==a){const e=Ja.NetworkProcessor.parseUrlString(a);a="null"===e.origin?e.origin:`${e.protocol}//${e.hostname}`}const r="default"===e.userContext?void 0:e.userContext;for(const[a,r]of Object.entries(e))void 0===a||void 0===r||["type","sourceOrigin","userContext"].includes(a)||t.set(a,r);return t.size>0&&this.#t?.(Wa.LogType.debugInfo,`Unsupported partition keys: ${JSON.stringify(Object.fromEntries(t))}`),{...void 0===a?{}:{sourceOrigin:a},...void 0===r?{}:{userContext:r}}}#da(e){return void 0===e?{}:"context"===e.type?this.#la(e):((0,Ha.assert)("storageKey"===e.type,"Unknown partition type"),this.#ha(e))}#ua(e,t){return void 0===t||!(void 0!==t.domain&&t.domain!==e.domain||void 0!==t.name&&t.name!==e.name||void 0!==t.value&&(t.value.type!==e.value.type||t.value.value!==e.value.value)||void 0!==t.path&&t.path!==e.path||void 0!==t.size&&t.size!==e.size||void 0!==t.httpOnly&&t.httpOnly!==e.httpOnly||void 0!==t.secure&&t.secure!==e.secure||void 0!==t.sameSite&&t.sameSite!==e.sameSite||void 0!==t.expiry&&t.expiry!==e.expiry)}};var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.OutgoingMessage=void 0;class Ya{#pa;#ma;constructor(e,t=null){this.#pa=e,this.#ma=t}static createFromPromise(e,t){return e.then((e=>"success"===e.kind?{kind:"success",value:new Ya(e.value,t)}:e))}static createResolved(e,t){return Promise.resolve({kind:"success",value:new Ya(e,t)})}get message(){return this.#pa}get channel(){return this.#ma}}Xa.OutgoingMessage=Ya,Object.defineProperty(f,"__esModule",{value:!0}),f.CommandProcessor=void 0;const Qa=g,er=s,tr=l,ar=E,rr=_,nr=N,sr=j,or=et,ir=Tt,cr=Nt,dr=Aa,ur=za,lr=Da,hr=Va,pr=$a,mr=Xa;class fr extends er.EventEmitter{#fa;#ga;#ya;#Sa;#va;#wa;#Ca;#ba;#xa;#Pa;#t;constructor(e,t,a,r,n,s,o,i,c,d=new ar.BidiNoOpParser,u){super(),this.#Pa=d,this.#t=u;const l=new cr.NetworkStorage,h=new ur.PreloadScriptStorage;this.#fa=new rr.BrowserProcessor(t),this.#ga=new sr.BrowsingContextProcessor(e,t,r,a,s,o,l,h,i,c,n,u),this.#ya=new nr.CdpProcessor(s,e,t),this.#Sa=new or.InputProcessor(s,o),this.#va=new ir.NetworkProcessor(s,l),this.#wa=new dr.PermissionsProcessor(t),this.#Ca=new lr.ScriptProcessor(s,o,h,u),this.#ba=new hr.SessionProcessor(a),this.#xa=new pr.StorageProcessor(t,s,u)}async#Ia(e){switch(e.method){case"session.end":case"session.new":break;case"browser.close":return this.#fa.close();case"browser.createUserContext":return await this.#fa.createUserContext();case"browser.getUserContexts":return await this.#fa.getUserContexts();case"browser.removeUserContext":return await this.#fa.removeUserContext(e.params.userContext);case"browsingContext.activate":return await this.#ga.activate(this.#Pa.parseActivateParams(e.params));case"browsingContext.captureScreenshot":return await this.#ga.captureScreenshot(this.#Pa.parseCaptureScreenshotParams(e.params));case"browsingContext.close":return await this.#ga.close(this.#Pa.parseCloseParams(e.params));case"browsingContext.create":return await this.#ga.create(this.#Pa.parseCreateParams(e.params));case"browsingContext.getTree":return this.#ga.getTree(this.#Pa.parseGetTreeParams(e.params));case"browsingContext.handleUserPrompt":return await this.#ga.handleUserPrompt(this.#Pa.parseHandleUserPromptParams(e.params));case"browsingContext.locateNodes":throw new Qa.UnsupportedOperationException(`Command '${e.method}' not yet implemented.`);case"browsingContext.navigate":return await this.#ga.navigate(this.#Pa.parseNavigateParams(e.params));case"browsingContext.print":return await this.#ga.print(this.#Pa.parsePrintParams(e.params));case"browsingContext.reload":return await this.#ga.reload(this.#Pa.parseReloadParams(e.params));case"browsingContext.setViewport":return await this.#ga.setViewport(this.#Pa.parseSetViewportParams(e.params));case"browsingContext.traverseHistory":return await this.#ga.traverseHistory(this.#Pa.parseTraverseHistoryParams(e.params));case"cdp.getSession":return this.#ya.getSession(this.#Pa.parseGetSessionParams(e.params));case"cdp.sendCommand":return await this.#ya.sendCommand(this.#Pa.parseSendCommandParams(e.params));case"input.performActions":return await this.#Sa.performActions(this.#Pa.parsePerformActionsParams(e.params));case"input.releaseActions":return await this.#Sa.releaseActions(this.#Pa.parseReleaseActionsParams(e.params));case"input.setFiles":return await this.#Sa.setFiles(this.#Pa.parseSetFilesParams(e.params));case"network.addIntercept":return await this.#va.addIntercept(this.#Pa.parseAddInterceptParams(e.params));case"network.continueRequest":return await this.#va.continueRequest(this.#Pa.parseContinueRequestParams(e.params));case"network.continueResponse":return await this.#va.continueResponse(this.#Pa.parseContinueResponseParams(e.params));case"network.continueWithAuth":return await this.#va.continueWithAuth(this.#Pa.parseContinueWithAuthParams(e.params));case"network.failRequest":return await this.#va.failRequest(this.#Pa.parseFailRequestParams(e.params));case"network.provideResponse":return await this.#va.provideResponse(this.#Pa.parseProvideResponseParams(e.params));case"network.removeIntercept":return await this.#va.removeIntercept(this.#Pa.parseRemoveInterceptParams(e.params));case"permissions.setPermission":return await this.#wa.setPermissions(this.#Pa.parseSetPermissionsParams(e.params));case"script.addPreloadScript":return await this.#Ca.addPreloadScript(this.#Pa.parseAddPreloadScriptParams(e.params));case"script.callFunction":return await this.#Ca.callFunction(this.#Pa.parseCallFunctionParams(e.params));case"script.disown":return await this.#Ca.disown(this.#Pa.parseDisownParams(e.params));case"script.evaluate":return await this.#Ca.evaluate(this.#Pa.parseEvaluateParams(e.params));case"script.getRealms":return this.#Ca.getRealms(this.#Pa.parseGetRealmsParams(e.params));case"script.removePreloadScript":return await this.#Ca.removePreloadScript(this.#Pa.parseRemovePreloadScriptParams(e.params));case"session.status":return this.#ba.status();case"session.subscribe":return this.#ba.subscribe(this.#Pa.parseSubscribeParams(e.params),e.channel);case"session.unsubscribe":return this.#ba.unsubscribe(this.#Pa.parseSubscribeParams(e.params),e.channel);case"storage.deleteCookies":throw new Qa.UnsupportedOperationException(`${e.method} is not supported yet`);case"storage.getCookies":return await this.#xa.getCookies(this.#Pa.parseGetCookiesParams(e.params));case"storage.setCookie":return await this.#xa.setCookie(this.#Pa.parseSetCookieParams(e.params))}throw new Qa.UnknownCommandException(`Unknown command '${e.method}'.`)}async processCommand(e){try{const t=await this.#Ia(e),a={type:"success",id:e.id,result:t};this.emit("response",{message:mr.OutgoingMessage.createResolved(a,e.channel),event:e.method})}catch(t){if(t instanceof Qa.Exception)this.emit("response",{message:mr.OutgoingMessage.createResolved(t.toErrorResponse(e.id),e.channel),event:e.method});else{const a=t;this.#t?.(tr.LogType.bidi,a),this.emit("response",{message:mr.OutgoingMessage.createResolved(new Qa.UnknownErrorException(a.message,a.stack).toErrorResponse(e.id),e.channel),event:e.method})}}}}f.CommandProcessor=fr;var gr={};Object.defineProperty(gr,"__esModule",{value:!0}),gr.BrowsingContextStorage=void 0;const yr=g;gr.BrowsingContextStorage=class{#oa=new Map;getTopLevelContexts(){return this.getAllContexts().filter((e=>e.isTopLevelContext()))}getAllContexts(){return Array.from(this.#oa.values())}deleteContextById(e){this.#oa.delete(e)}deleteContext(e){this.#oa.delete(e.id)}addContext(e){this.#oa.set(e.id,e)}hasContext(e){return this.#oa.has(e)}findContext(e){return this.#oa.get(e)}findTopLevelContextId(e){if(null===e)return null;const t=this.findContext(e),a=t?.parentId??null;return null===a?e:this.findTopLevelContextId(a)}findContextBySession(e){for(const t of this.#oa.values())if(t.cdpTarget.cdpSessionId===e)return t}getContext(e){const t=this.findContext(e);if(void 0===t)throw new yr.NoSuchFrameException(`Context ${e} not found`);return t}};var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0}),Sr.RealmStorage=void 0;const vr=g,wr=Q;Sr.RealmStorage=class{#ka=new Map;#Ra=new Map;get knownHandlesToRealmMap(){return this.#ka}addRealm(e){this.#Ra.set(e.realmId,e)}findRealms(e){return Array.from(this.#Ra.values()).filter((t=>(void 0===e.realmId||e.realmId===t.realmId)&&(!(void 0!==e.browsingContextId&&!t.associatedBrowsingContexts.map((e=>e.id)).includes(e.browsingContextId))&&((void 0===e.sandbox||t instanceof wr.WindowRealm&&e.sandbox===t.sandbox)&&((void 0===e.executionContextId||e.executionContextId===t.executionContextId)&&((void 0===e.origin||e.origin===t.origin)&&((void 0===e.type||e.type===t.realmType)&&(void 0===e.cdpSessionId||e.cdpSessionId===t.cdpClient.sessionId))))))))}findRealm(e){const t=this.findRealms(e);if(1===t.length)return t[0]}getRealm(e){const t=this.findRealm(e);if(void 0===t)throw new vr.NoSuchFrameException(`Realm ${JSON.stringify(e)} not found`);return t}deleteRealms(e){this.findRealms(e).map((e=>{e.dispose(),this.#Ra.delete(e.realmId),Array.from(this.knownHandlesToRealmMap.entries()).filter((([,t])=>t===e.realmId)).map((([e])=>this.knownHandlesToRealmMap.delete(e)))}))}};var Cr={},br={};Object.defineProperty(br,"__esModule",{value:!0}),br.Buffer=void 0;br.Buffer=class{#Ea;#_a=[];#Ta;constructor(e,t){this.#Ea=e,this.#Ta=t}get(){return this.#_a}add(e){for(this.#_a.push(e);this.#_a.length>this.#Ea;){const e=this.#_a.shift();void 0!==e&&this.#Ta?.(e)}}};var xr={};Object.defineProperty(xr,"__esModule",{value:!0}),xr.DefaultMap=void 0;class Pr extends Map{#Na;constructor(e,t){super(t),this.#Na=e}get(e){return this.has(e)||this.set(e,this.#Na(e)),super.get(e)}}xr.DefaultMap=Pr;var Ir={};Object.defineProperty(Ir,"__esModule",{value:!0}),Ir.IdWrapper=void 0;class kr{static#ja=0;#u;constructor(){this.#u=++kr.#ja}get id(){return this.#u}}Ir.IdWrapper=kr;var Rr={};Object.defineProperty(Rr,"__esModule",{value:!0}),Rr.assertSupportedEvent=Rr.isCdpEvent=void 0;const Er=g;function _r(e){return e.split(".").at(0)?.startsWith(Er.ChromiumBidi.BiDiModule.Cdp)??!1}Rr.isCdpEvent=_r,Rr.assertSupportedEvent=function(e){if(!Er.ChromiumBidi.EVENT_NAMES.has(e)&&!_r(e))throw new Er.InvalidArgumentException(`Unknown event: ${e}`)};var Tr={};Object.defineProperty(Tr,"__esModule",{value:!0}),Tr.SubscriptionManager=Tr.unrollEvents=Tr.cartesianProduct=void 0;const Nr=g,jr=Rr;function Or(...e){return e.reduce(((e,t)=>e.flatMap((e=>t.map((t=>[e,t].flat()))))))}function Mr(e){const t=new Set;function a(e){for(const a of e)t.add(a)}for(const r of e)switch(r){case Nr.ChromiumBidi.BiDiModule.BrowsingContext:a(Object.values(Nr.ChromiumBidi.BrowsingContext.EventNames));break;case Nr.ChromiumBidi.BiDiModule.Log:a(Object.values(Nr.ChromiumBidi.Log.EventNames));break;case Nr.ChromiumBidi.BiDiModule.Network:a(Object.values(Nr.ChromiumBidi.Network.EventNames));break;case Nr.ChromiumBidi.BiDiModule.Script:a(Object.values(Nr.ChromiumBidi.Script.EventNames));break;default:t.add(r)}return[...t.values()]}Tr.cartesianProduct=Or,Tr.unrollEvents=Mr;Tr.SubscriptionManager=class{#Oa=0;#Ma=new Map;#i;constructor(e){this.#i=e}getChannelsSubscribedToEvent(e,t){return Array.from(this.#Ma.keys()).map((a=>({priority:this.#Aa(e,t,a),channel:a}))).filter((({priority:e})=>null!==e)).sort(((e,t)=>e.priority-t.priority)).map((({channel:e})=>e))}#Aa(e,t,a){const r=this.#Ma.get(a);if(void 0===r)return null;const n=this.#i.findTopLevelContextId(t),s=[...new Set([null,n])].map((t=>{const a=r.get(t)?.get(e);if((0,jr.isCdpEvent)(e)){const e=r.get(t)?.get(Nr.ChromiumBidi.BiDiModule.Cdp);return a&&e?Math.min(a,e):a??e}return a})).filter((e=>void 0!==e));return 0===s.length?null:Math.min(...s)}subscribe(e,t,a){switch(t=this.#i.findTopLevelContextId(t),e){case Nr.ChromiumBidi.BiDiModule.BrowsingContext:return void Object.values(Nr.ChromiumBidi.BrowsingContext.EventNames).map((e=>this.subscribe(e,t,a)));case Nr.ChromiumBidi.BiDiModule.Log:return void Object.values(Nr.ChromiumBidi.Log.EventNames).map((e=>this.subscribe(e,t,a)));case Nr.ChromiumBidi.BiDiModule.Network:return void Object.values(Nr.ChromiumBidi.Network.EventNames).map((e=>this.subscribe(e,t,a)));case Nr.ChromiumBidi.BiDiModule.Script:return void Object.values(Nr.ChromiumBidi.Script.EventNames).map((e=>this.subscribe(e,t,a)))}this.#Ma.has(a)||this.#Ma.set(a,new Map);const r=this.#Ma.get(a);r.has(t)||r.set(t,new Map);const n=r.get(t);n.has(e)||n.set(e,this.#Oa++)}unsubscribeAll(e,t,a){for(const e of t)null!==e&&this.#i.getContext(e);Or(Mr(e),t).map((([e,t])=>this.#Ba(e,t,a))).forEach((e=>e()))}unsubscribe(e,t,a){this.unsubscribeAll([e],[t],a)}#Ba(e,t,a){if(t=this.#i.findTopLevelContextId(t),!this.#Ma.has(a))throw new Nr.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);const r=this.#Ma.get(a);if(!r.has(t))throw new Nr.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);const n=r.get(t);if(!n.has(e))throw new Nr.InvalidArgumentException(`Cannot unsubscribe from ${e}, ${null===t?"null":t}. No subscription found.`);return()=>{n.delete(e),0===n.size&&r.delete(e),0===r.size&&this.#Ma.delete(a)}}},Object.defineProperty(Cr,"__esModule",{value:!0}),Cr.EventManager=void 0;const Ar=g,Br=br,zr=xr,Dr=s,Lr=Ir,Zr=Xa,Ur=Rr,Fr=Tr;class qr{#za=new Lr.IdWrapper;#Da;#La;constructor(e,t){this.#La=e,this.#Da=t}get id(){return this.#za.id}get contextId(){return this.#Da}get event(){return this.#La}}const Vr=new Map([[Ar.ChromiumBidi.Log.EventNames.LogEntryAdded,100]]);class $r extends Dr.EventEmitter{#Za=new zr.DefaultMap((()=>new Set));#Ua=new Map;#Fa=new Map;#qa;#i;constructor(e){super(),this.#i=e,this.#qa=new Fr.SubscriptionManager(e)}static#Va(e,t,a){return JSON.stringify({eventName:e,browsingContext:t,channel:a})}registerEvent(e,t){this.registerPromiseEvent(Promise.resolve({kind:"success",value:e}),t,e.method)}registerPromiseEvent(e,t,a){const r=new qr(e,t),n=this.#qa.getChannelsSubscribedToEvent(a,t);this.#$a(r,a);for(const t of n)this.emit("event",{message:Zr.OutgoingMessage.createFromPromise(e,t),event:a}),this.#Ka(r,t,a)}subscribe(e,t,a){for(const t of e)(0,Ur.assertSupportedEvent)(t);for(const e of t)null!==e&&this.#i.getContext(e);for(const r of e)for(const e of t){this.#qa.subscribe(r,e,a);for(const t of this.#Ha(r,e,a))this.emit("event",{message:Zr.OutgoingMessage.createFromPromise(t.event,a),event:r}),this.#Ka(t,a,r)}}unsubscribe(e,t,a){for(const t of e)(0,Ur.assertSupportedEvent)(t);this.#qa.unsubscribeAll(e,t,a)}#$a(e,t){if(!Vr.has(t))return;const a=$r.#Va(t,e.contextId);this.#Ua.has(a)||this.#Ua.set(a,new Br.Buffer(Vr.get(t))),this.#Ua.get(a).add(e),this.#Za.get(t).add(e.contextId)}#Ka(e,t,a){if(!Vr.has(a))return;const r=$r.#Va(a,e.contextId,t);this.#Fa.set(r,Math.max(this.#Fa.get(r)??0,e.id))}#Ha(e,t,a){const r=$r.#Va(e,t),n=$r.#Va(e,t,a),s=this.#Fa.get(n)??-1/0,o=this.#Ua.get(r)?.get().filter((e=>e.id>s))??[];return null===t&&Array.from(this.#Za.get(e).keys()).filter((e=>null!==e&&this.#i.hasContext(e))).map((t=>this.#Ha(e,t,a))).forEach((e=>o.push(...e))),o.sort(((e,t)=>e.id-t.id))}}Cr.EventManager=$r,Object.defineProperty(n,"__esModule",{value:!0}),n.BidiServer=void 0;const Kr=s,Hr=l,Wr=h,Jr=f,Gr=gr,Xr=Sr,Yr=Cr;class Qr extends Kr.EventEmitter{#Wa;#Ja;#Ga;#y;#i=new Gr.BrowsingContextStorage;#t;#Xa=e=>{this.#Ga.processCommand(e).catch((e=>{this.#t?.(Hr.LogType.debugError,e)}))};#Ya=async e=>{const t=e.message;null!==e.channel&&(t.channel=e.channel),await this.#Ja.sendMessage(t)};constructor(e,t,a,r,n,s,o,i){super(),this.#t=i,this.#Wa=new Wr.ProcessingQueue(this.#Ya,this.#t),this.#Ja=e,this.#Ja.setOnMessage(this.#Xa),this.#y=new Yr.EventManager(this.#i),this.#Ga=new Jr.CommandProcessor(t,a,this.#y,r,n,this.#i,new Xr.RealmStorage,s?.acceptInsecureCerts??!1,s?.sharedIdWithFrame??!1,o,this.#t),this.#y.on("event",(({message:e,event:t})=>{this.emitOutgoingMessage(e,t)})),this.#Ga.on("response",(({message:e,event:t})=>{this.emitOutgoingMessage(e,t)}))}static async createAndStart(e,t,a,r,n,s,o){const[{browserContextIds:i},{targetInfos:c}]=await Promise.all([a.sendCommand("Target.getBrowserContexts"),a.sendCommand("Target.getTargets")]);let d="default";for(const e of c)if(e.browserContextId&&!i.includes(e.browserContextId)){d=e.browserContextId;break}const u=new Qr(e,t,a,r,d,n,s,o);return await a.sendCommand("Target.setDiscoverTargets",{discover:!0}),await a.sendCommand("Target.setAutoAttach",{autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),await u.#Qa(),u}emitOutgoingMessage(e,t){this.#Wa.add(e,t)}close(){this.#Ja.close()}async#Qa(){await Promise.all(this.#i.getTopLevelContexts().map((e=>e.lifecycleLoaded())))}}n.BidiServer=Qr,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.OutgoingMessage=e.EventEmitter=e.BidiServer=void 0;var t=n;Object.defineProperty(e,"BidiServer",{enumerable:!0,get:function(){return t.BidiServer}});var a=s;Object.defineProperty(e,"EventEmitter",{enumerable:!0,get:function(){return a.EventEmitter}});var r=Xa;Object.defineProperty(e,"OutgoingMessage",{enumerable:!0,get:function(){return r.OutgoingMessage}})}(r);var en={},tn={};Object.defineProperty(tn,"__esModule",{value:!0}),tn.MapperCdpClient=tn.CloseError=void 0;const an=s;class rn extends Error{}tn.CloseError=rn;class nn extends an.EventEmitter{#c;#er;constructor(e,t){super(),this.#c=e,this.#er=t}get sessionId(){return this.#er}sendCommand(e,...t){return this.#c.sendCommand(e,t[0],this.#er)}isCloseError(e){return e instanceof rn}}tn.MapperCdpClient=nn,Object.defineProperty(en,"__esModule",{value:!0}),en.MapperCdpConnection=void 0;const sn=l,on=tn;class cn{static LOGGER_PREFIX_RECV=`${sn.LogType.cdp}:RECV \u25c2`;static LOGGER_PREFIX_SEND=`${sn.LogType.cdp}:SEND \u25b8`;#tr;#Ja;#ar=new Map;#rr=new Map;#t;#nr=0;constructor(e,t){this.#Ja=e,this.#t=t,this.#Ja.setOnMessage(this.#sr),this.#tr=this.#or(void 0)}close(){this.#Ja.close();for(const[,{reject:e,error:t}]of this.#rr)e(t);this.#rr.clear(),this.#ar.clear()}async createBrowserSession(){const{sessionId:e}=await this.#tr.sendCommand("Target.attachToBrowserTarget");return this.#or(e)}getCdpClient(e){const t=this.#ar.get(e);if(!t)throw new Error(`Unknown CDP session ID: ${e}`);return t}sendCommand(e,t,a){return new Promise(((r,n)=>{const s=this.#nr++;this.#rr.set(s,{resolve:r,reject:n,error:new on.CloseError(`${e} ${JSON.stringify(t)} ${a??""} call rejected because the connection has been closed.`)});const o={id:s,method:e,params:t};a&&(o.sessionId=a),this.#Ja.sendMessage(JSON.stringify(o))?.catch((e=>{this.#t?.(sn.LogType.debugError,e),this.#Ja.close()})),this.#t?.(cn.LOGGER_PREFIX_SEND,o)}))}#sr=e=>{const t=JSON.parse(e);if(this.#t?.(cn.LOGGER_PREFIX_RECV,t),"Target.attachedToTarget"===t.method){const{sessionId:e}=t.params;this.#or(e)}if(void 0!==t.id){const e=this.#rr.get(t.id);this.#rr.delete(t.id),e&&(t.result?e.resolve(t.result):t.error&&e.reject(t.error))}else if(t.method){const e=this.#ar.get(t.sessionId??void 0);if(e?.emit(t.method,t.params||{}),"Target.detachedFromTarget"===t.method){const{sessionId:e}=t.params,a=this.#ar.get(e);a&&(this.#ar.delete(e),a.removeAllListeners())}}};#or(e){const t=new on.MapperCdpClient(this,e);return this.#ar.set(e,t),t}}en.MapperCdpConnection=cn;var dn={},un={},ln={},hn={},pn={},mn={},fn={};!function(e){var t;Object.defineProperty(e,"__esModule",{value:!0}),e.getParsedType=e.ZodParsedType=e.objectUtil=e.util=void 0,function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw new Error},e.arrayToEnum=e=>{const t={};for(const a of e)t[a]=a;return t},e.getValidEnumValues=t=>{const a=e.objectKeys(t).filter((e=>"number"!=typeof t[t[e]])),r={};for(const e of a)r[e]=t[e];return e.objectValues(r)},e.objectValues=t=>e.objectKeys(t).map((function(e){return t[e]})),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.push(a);return t},e.find=(e,t)=>{for(const a of e)if(t(a))return a},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},e.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t}(t=e.util||(e.util={})),(e.objectUtil||(e.objectUtil={})).mergeShapes=(e,t)=>({...e,...t}),e.ZodParsedType=t.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]);e.getParsedType=t=>{switch(typeof t){case"undefined":return e.ZodParsedType.undefined;case"string":return e.ZodParsedType.string;case"number":return isNaN(t)?e.ZodParsedType.nan:e.ZodParsedType.number;case"boolean":return e.ZodParsedType.boolean;case"function":return e.ZodParsedType.function;case"bigint":return e.ZodParsedType.bigint;case"symbol":return e.ZodParsedType.symbol;case"object":return Array.isArray(t)?e.ZodParsedType.array:null===t?e.ZodParsedType.null:t.then&&"function"==typeof t.then&&t.catch&&"function"==typeof t.catch?e.ZodParsedType.promise:"undefined"!=typeof Map&&t instanceof Map?e.ZodParsedType.map:"undefined"!=typeof Set&&t instanceof Set?e.ZodParsedType.set:"undefined"!=typeof Date&&t instanceof Date?e.ZodParsedType.date:e.ZodParsedType.object;default:return e.ZodParsedType.unknown}}}(fn);var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.ZodError=gn.quotelessJson=gn.ZodIssueCode=void 0;const yn=fn;gn.ZodIssueCode=yn.util.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);gn.quotelessJson=e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:");class Sn extends Error{constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){const t=e||function(e){return e.message},a={_errors:[]},r=e=>{for(const n of e.issues)if("invalid_union"===n.code)n.unionErrors.map(r);else if("invalid_return_type"===n.code)r(n.returnTypeError);else if("invalid_arguments"===n.code)r(n.argumentsError);else if(0===n.path.length)a._errors.push(t(n));else{let e=a,r=0;for(;r<n.path.length;){const a=n.path[r];r===n.path.length-1?(e[a]=e[a]||{_errors:[]},e[a]._errors.push(t(n))):e[a]=e[a]||{_errors:[]},e=e[a],r++}}};return r(this),a}toString(){return this.message}get message(){return JSON.stringify(this.issues,yn.util.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=(e=>e.message)){const t={},a=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):a.push(e(r));return{formErrors:a,fieldErrors:t}}get formErrors(){return this.flatten()}}gn.ZodError=Sn,Sn.create=e=>new Sn(e),Object.defineProperty(mn,"__esModule",{value:!0});const vn=fn,wn=gn;mn.default=(e,t)=>{let a;switch(e.code){case wn.ZodIssueCode.invalid_type:a=e.received===vn.ZodParsedType.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case wn.ZodIssueCode.invalid_literal:a=`Invalid literal value, expected ${JSON.stringify(e.expected,vn.util.jsonStringifyReplacer)}`;break;case wn.ZodIssueCode.unrecognized_keys:a=`Unrecognized key(s) in object: ${vn.util.joinValues(e.keys,", ")}`;break;case wn.ZodIssueCode.invalid_union:a="Invalid input";break;case wn.ZodIssueCode.invalid_union_discriminator:a=`Invalid discriminator value. Expected ${vn.util.joinValues(e.options)}`;break;case wn.ZodIssueCode.invalid_enum_value:a=`Invalid enum value. Expected ${vn.util.joinValues(e.options)}, received '${e.received}'`;break;case wn.ZodIssueCode.invalid_arguments:a="Invalid function arguments";break;case wn.ZodIssueCode.invalid_return_type:a="Invalid function return type";break;case wn.ZodIssueCode.invalid_date:a="Invalid date";break;case wn.ZodIssueCode.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(a=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(a=`${a} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?a=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?a=`Invalid input: must end with "${e.validation.endsWith}"`:vn.util.assertNever(e.validation):a="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case wn.ZodIssueCode.too_small:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case wn.ZodIssueCode.too_big:a="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case wn.ZodIssueCode.custom:a="Invalid input";break;case wn.ZodIssueCode.invalid_intersection_types:a="Intersection results could not be merged";break;case wn.ZodIssueCode.not_multiple_of:a=`Number must be a multiple of ${e.multipleOf}`;break;case wn.ZodIssueCode.not_finite:a="Number must be finite";break;default:a=t.defaultError,vn.util.assertNever(e)}return{message:a}};var Cn=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(pn,"__esModule",{value:!0}),pn.getErrorMap=pn.setErrorMap=pn.defaultErrorMap=void 0;const bn=Cn(mn);pn.defaultErrorMap=bn.default;let xn=bn.default;pn.setErrorMap=function(e){xn=e},pn.getErrorMap=function(){return xn};var Pn={};!function(t){var a=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.isAsync=t.isValid=t.isDirty=t.isAborted=t.OK=t.DIRTY=t.INVALID=t.ParseStatus=t.addIssueToContext=t.EMPTY_PATH=t.makeIssue=void 0;const r=pn,n=a(mn);t.makeIssue=e=>{const{data:t,path:a,errorMaps:r,issueData:n}=e,s=[...a,...n.path||[]],o={...n,path:s};let i="";const c=r.filter((e=>!!e)).slice().reverse();for(const e of c)i=e(o,{data:t,defaultError:i}).message;return{...n,path:s,message:n.message||i}},t.EMPTY_PATH=[],t.addIssueToContext=function(e,a){const s=(0,t.makeIssue)({issueData:a,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,(0,r.getErrorMap)(),n.default].filter((e=>!!e))});e.common.issues.push(s)};class s{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,a){const r=[];for(const n of a){if("aborted"===n.status)return t.INVALID;"dirty"===n.status&&e.dirty(),r.push(n.value)}return{status:e.value,value:r}}static async mergeObjectAsync(e,t){const a=[];for(const e of t)a.push({key:await e.key,value:await e.value});return s.mergeObjectSync(e,a)}static mergeObjectSync(e,a){const r={};for(const n of a){const{key:a,value:s}=n;if("aborted"===a.status)return t.INVALID;if("aborted"===s.status)return t.INVALID;"dirty"===a.status&&e.dirty(),"dirty"===s.status&&e.dirty(),"__proto__"===a.value||void 0===s.value&&!n.alwaysSet||(r[a.value]=s.value)}return{status:e.value,value:r}}}t.ParseStatus=s,t.INVALID=Object.freeze({status:"aborted"});t.DIRTY=e=>({status:"dirty",value:e});t.OK=e=>({status:"valid",value:e});t.isAborted=e=>"aborted"===e.status;t.isDirty=e=>"dirty"===e.status;t.isValid=e=>"valid"===e.status;t.isAsync=e=>"undefined"!=typeof Promise&&e instanceof Promise}(Pn);var In={};Object.defineProperty(In,"__esModule",{value:!0});var kn,Rn={},En={};kn=En,Object.defineProperty(kn,"__esModule",{value:!0}),kn.errorUtil=void 0,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:null==e?void 0:e.message}(kn.errorUtil||(kn.errorUtil={})),function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.date=e.boolean=e.bigint=e.array=e.any=e.coerce=e.ZodFirstPartyTypeKind=e.late=e.ZodSchema=e.Schema=e.custom=e.ZodReadonly=e.ZodPipeline=e.ZodBranded=e.BRAND=e.ZodNaN=e.ZodCatch=e.ZodDefault=e.ZodNullable=e.ZodOptional=e.ZodTransformer=e.ZodEffects=e.ZodPromise=e.ZodNativeEnum=e.ZodEnum=e.ZodLiteral=e.ZodLazy=e.ZodFunction=e.ZodSet=e.ZodMap=e.ZodRecord=e.ZodTuple=e.ZodIntersection=e.ZodDiscriminatedUnion=e.ZodUnion=e.ZodObject=e.ZodArray=e.ZodVoid=e.ZodNever=e.ZodUnknown=e.ZodAny=e.ZodNull=e.ZodUndefined=e.ZodSymbol=e.ZodDate=e.ZodBoolean=e.ZodBigInt=e.ZodNumber=e.ZodString=e.ZodType=void 0,e.NEVER=e.void=e.unknown=e.union=e.undefined=e.tuple=e.transformer=e.symbol=e.string=e.strictObject=e.set=e.record=e.promise=e.preprocess=e.pipeline=e.ostring=e.optional=e.onumber=e.oboolean=e.object=e.number=e.nullable=e.null=e.never=e.nativeEnum=e.nan=e.map=e.literal=e.lazy=e.intersection=e.instanceof=e.function=e.enum=e.effect=e.discriminatedUnion=void 0;const t=pn,a=En,r=Pn,n=fn,s=gn;class o{constructor(e,t,a,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=a,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const i=(e,t)=>{if((0,r.isValid)(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new s.ZodError(e.common.issues);return this._error=t,this._error}}};function c(e){if(!e)return{};const{errorMap:t,invalid_type_error:a,required_error:r,description:n}=e;if(t&&(a||r))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:n};return{errorMap:(e,t)=>"invalid_type"!==e.code?{message:t.defaultError}:void 0===t.data?{message:null!=r?r:t.defaultError}:{message:null!=a?a:t.defaultError},description:n}}class d{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return(0,n.getParsedType)(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:(0,n.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new r.ParseStatus,ctx:{common:e.parent.common,data:e.data,parsedType:(0,n.getParsedType)(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if((0,r.isAsync)(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const a=this.safeParse(e,t);if(a.success)return a.data;throw a.error}safeParse(e,t){var a;const r={common:{issues:[],async:null!==(a=null==t?void 0:t.async)&&void 0!==a&&a,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,n.getParsedType)(e)},s=this._parseSync({data:e,path:r.path,parent:r});return i(r,s)}async parseAsync(e,t){const a=await this.safeParseAsync(e,t);if(a.success)return a.data;throw a.error}async safeParseAsync(e,t){const a={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:(0,n.getParsedType)(e)},s=this._parse({data:e,path:a.path,parent:a}),o=await((0,r.isAsync)(s)?s:Promise.resolve(s));return i(a,o)}refine(e,t){const a=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,r)=>{const n=e(t),o=()=>r.addIssue({code:s.ZodIssueCode.custom,...a(t)});return"undefined"!=typeof Promise&&n instanceof Promise?n.then((e=>!!e||(o(),!1))):!!n||(o(),!1)}))}refinement(e,t){return this._refinement(((a,r)=>!!e(a)||(r.addIssue("function"==typeof t?t(a,r):t),!1)))}_refinement(e){return new G({schema:this,typeName:se.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return X.create(this,this._def)}nullable(){return Y.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return N.create(this,this._def)}promise(){return J.create(this,this._def)}or(e){return M.create([this,e],this._def)}and(e){return D.create(this,e,this._def)}transform(e){return new G({...c(this._def),schema:this,typeName:se.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Q({...c(this._def),innerType:this,defaultValue:t,typeName:se.ZodDefault})}brand(){return new ae({typeName:se.ZodBranded,type:this,...c(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new ee({...c(this._def),innerType:this,catchValue:t,typeName:se.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return re.create(this,e)}readonly(){return ne.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}e.ZodType=d,e.Schema=d,e.ZodSchema=d;const u=/^c[^\s-]{8,}$/i,l=/^[a-z][a-z0-9]*$/,h=/^[0-9A-HJKMNP-TV-Z]{26}$/,p=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,m=/^(?!\.)(?!.*\.\.)([A-Z0-9_+-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let f;const g=/^(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))$/,y=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/;class S extends d{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==n.ZodParsedType.string){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.string,received:t.parsedType}),r.INVALID}const t=new r.ParseStatus;let a;for(const d of this._def.checks)if("min"===d.kind)e.data.length<d.value&&(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),t.dirty());else if("max"===d.kind)e.data.length>d.value&&(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!1,message:d.message}),t.dirty());else if("length"===d.kind){const n=e.data.length>d.value,o=e.data.length<d.value;(n||o)&&(a=this._getOrReturnCtx(e,a),n?(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_big,maximum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}):o&&(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_small,minimum:d.value,type:"string",inclusive:!0,exact:!0,message:d.message}),t.dirty())}else if("email"===d.kind)m.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"email",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("emoji"===d.kind)f||(f=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),f.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"emoji",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("uuid"===d.kind)p.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"uuid",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("cuid"===d.kind)u.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"cuid",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("cuid2"===d.kind)l.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"cuid2",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("ulid"===d.kind)h.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"ulid",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty());else if("url"===d.kind)try{new URL(e.data)}catch(n){a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"url",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty()}else if("regex"===d.kind){d.regex.lastIndex=0;d.regex.test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"regex",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty())}else if("trim"===d.kind)e.data=e.data.trim();else if("includes"===d.kind)e.data.includes(d.value,d.position)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_string,validation:{includes:d.value,position:d.position},message:d.message}),t.dirty());else if("toLowerCase"===d.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===d.kind)e.data=e.data.toUpperCase();else if("startsWith"===d.kind)e.data.startsWith(d.value)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_string,validation:{startsWith:d.value},message:d.message}),t.dirty());else if("endsWith"===d.kind)e.data.endsWith(d.value)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_string,validation:{endsWith:d.value},message:d.message}),t.dirty());else if("datetime"===d.kind){((c=d).precision?c.offset?new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${c.precision}}(([+-]\\d{2}(:?\\d{2})?)|Z)$`):new RegExp(`^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}\\.\\d{${c.precision}}Z$`):0===c.precision?c.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}Z$"):c.offset?new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?(([+-]\\d{2}(:?\\d{2})?)|Z)$"):new RegExp("^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$")).test(e.data)||(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_string,validation:"datetime",message:d.message}),t.dirty())}else"ip"===d.kind?(o=e.data,("v4"!==(i=d.version)&&i||!g.test(o))&&("v6"!==i&&i||!y.test(o))&&(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{validation:"ip",code:s.ZodIssueCode.invalid_string,message:d.message}),t.dirty())):n.util.assertNever(d);var o,i,c;return{status:t.value,value:e.data}}_regex(e,t,r){return this.refinement((t=>e.test(t)),{validation:t,code:s.ZodIssueCode.invalid_string,...a.errorUtil.errToObj(r)})}_addCheck(e){return new S({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...a.errorUtil.errToObj(e)})}url(e){return this._addCheck({kind:"url",...a.errorUtil.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...a.errorUtil.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...a.errorUtil.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...a.errorUtil.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...a.errorUtil.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...a.errorUtil.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...a.errorUtil.errToObj(e)})}datetime(e){var t;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t&&t,...a.errorUtil.errToObj(null==e?void 0:e.message)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...a.errorUtil.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...a.errorUtil.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...a.errorUtil.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...a.errorUtil.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...a.errorUtil.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...a.errorUtil.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...a.errorUtil.errToObj(t)})}nonempty(e){return this.min(1,a.errorUtil.errToObj(e))}trim(){return new S({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new S({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new S({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function v(e,t){const a=(e.toString().split(".")[1]||"").length,r=(t.toString().split(".")[1]||"").length,n=a>r?a:r;return parseInt(e.toFixed(n).replace(".",""))%parseInt(t.toFixed(n).replace(".",""))/Math.pow(10,n)}e.ZodString=S,S.create=e=>{var t;return new S({checks:[],typeName:se.ZodString,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...c(e)})};class w extends d{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==n.ZodParsedType.number){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.number,received:t.parsedType}),r.INVALID}let t;const a=new r.ParseStatus;for(const o of this._def.checks)if("int"===o.kind)n.util.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:"integer",received:"float",message:o.message}),a.dirty());else if("min"===o.kind){(o.inclusive?e.data<o.value:e.data<=o.value)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),a.dirty())}else if("max"===o.kind){(o.inclusive?e.data>o.value:e.data>=o.value)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),a.dirty())}else"multipleOf"===o.kind?0!==v(e.data,o.value)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.not_multiple_of,multipleOf:o.value,message:o.message}),a.dirty()):"finite"===o.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.not_finite,message:o.message}),a.dirty()):n.util.assertNever(o);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,a.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,a.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,a.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,a.errorUtil.toString(t))}setLimit(e,t,r,n){return new w({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:a.errorUtil.toString(n)}]})}_addCheck(e){return new w({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:a.errorUtil.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:a.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:a.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:a.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:a.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:a.errorUtil.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:a.errorUtil.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:a.errorUtil.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:a.errorUtil.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&n.util.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const a of this._def.checks){if("finite"===a.kind||"int"===a.kind||"multipleOf"===a.kind)return!0;"min"===a.kind?(null===t||a.value>t)&&(t=a.value):"max"===a.kind&&(null===e||a.value<e)&&(e=a.value)}return Number.isFinite(t)&&Number.isFinite(e)}}e.ZodNumber=w,w.create=e=>new w({checks:[],typeName:se.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...c(e)});class C extends d{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){this._def.coerce&&(e.data=BigInt(e.data));if(this._getType(e)!==n.ZodParsedType.bigint){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.bigint,received:t.parsedType}),r.INVALID}let t;const a=new r.ParseStatus;for(const o of this._def.checks)if("min"===o.kind){(o.inclusive?e.data<o.value:e.data<=o.value)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_small,type:"bigint",minimum:o.value,inclusive:o.inclusive,message:o.message}),a.dirty())}else if("max"===o.kind){(o.inclusive?e.data>o.value:e.data>=o.value)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_big,type:"bigint",maximum:o.value,inclusive:o.inclusive,message:o.message}),a.dirty())}else"multipleOf"===o.kind?e.data%o.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.not_multiple_of,multipleOf:o.value,message:o.message}),a.dirty()):n.util.assertNever(o);return{status:a.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,a.errorUtil.toString(t))}gt(e,t){return this.setLimit("min",e,!1,a.errorUtil.toString(t))}lte(e,t){return this.setLimit("max",e,!0,a.errorUtil.toString(t))}lt(e,t){return this.setLimit("max",e,!1,a.errorUtil.toString(t))}setLimit(e,t,r,n){return new C({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:r,message:a.errorUtil.toString(n)}]})}_addCheck(e){return new C({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:a.errorUtil.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:a.errorUtil.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:a.errorUtil.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:a.errorUtil.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:a.errorUtil.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}e.ZodBigInt=C,C.create=e=>{var t;return new C({checks:[],typeName:se.ZodBigInt,coerce:null!==(t=null==e?void 0:e.coerce)&&void 0!==t&&t,...c(e)})};class b extends d{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==n.ZodParsedType.boolean){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.boolean,received:t.parsedType}),r.INVALID}return(0,r.OK)(e.data)}}e.ZodBoolean=b,b.create=e=>new b({typeName:se.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...c(e)});class x extends d{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==n.ZodParsedType.date){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.date,received:t.parsedType}),r.INVALID}if(isNaN(e.data.getTime())){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_date}),r.INVALID}const t=new r.ParseStatus;let a;for(const o of this._def.checks)"min"===o.kind?e.data.getTime()<o.value&&(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),t.dirty()):"max"===o.kind?e.data.getTime()>o.value&&(a=this._getOrReturnCtx(e,a),(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),t.dirty()):n.util.assertNever(o);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new x({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:a.errorUtil.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:a.errorUtil.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}e.ZodDate=x,x.create=e=>new x({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:se.ZodDate,...c(e)});class P extends d{_parse(e){if(this._getType(e)!==n.ZodParsedType.symbol){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.symbol,received:t.parsedType}),r.INVALID}return(0,r.OK)(e.data)}}e.ZodSymbol=P,P.create=e=>new P({typeName:se.ZodSymbol,...c(e)});class I extends d{_parse(e){if(this._getType(e)!==n.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.undefined,received:t.parsedType}),r.INVALID}return(0,r.OK)(e.data)}}e.ZodUndefined=I,I.create=e=>new I({typeName:se.ZodUndefined,...c(e)});class k extends d{_parse(e){if(this._getType(e)!==n.ZodParsedType.null){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.null,received:t.parsedType}),r.INVALID}return(0,r.OK)(e.data)}}e.ZodNull=k,k.create=e=>new k({typeName:se.ZodNull,...c(e)});class R extends d{constructor(){super(...arguments),this._any=!0}_parse(e){return(0,r.OK)(e.data)}}e.ZodAny=R,R.create=e=>new R({typeName:se.ZodAny,...c(e)});class E extends d{constructor(){super(...arguments),this._unknown=!0}_parse(e){return(0,r.OK)(e.data)}}e.ZodUnknown=E,E.create=e=>new E({typeName:se.ZodUnknown,...c(e)});class _ extends d{_parse(e){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.never,received:t.parsedType}),r.INVALID}}e.ZodNever=_,_.create=e=>new _({typeName:se.ZodNever,...c(e)});class T extends d{_parse(e){if(this._getType(e)!==n.ZodParsedType.undefined){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.void,received:t.parsedType}),r.INVALID}return(0,r.OK)(e.data)}}e.ZodVoid=T,T.create=e=>new T({typeName:se.ZodVoid,...c(e)});class N extends d{_parse(e){const{ctx:t,status:a}=this._processInputParams(e),i=this._def;if(t.parsedType!==n.ZodParsedType.array)return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.array,received:t.parsedType}),r.INVALID;if(null!==i.exactLength){const e=t.data.length>i.exactLength.value,n=t.data.length<i.exactLength.value;(e||n)&&((0,r.addIssueToContext)(t,{code:e?s.ZodIssueCode.too_big:s.ZodIssueCode.too_small,minimum:n?i.exactLength.value:void 0,maximum:e?i.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:i.exactLength.message}),a.dirty())}if(null!==i.minLength&&t.data.length<i.minLength.value&&((0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_small,minimum:i.minLength.value,type:"array",inclusive:!0,exact:!1,message:i.minLength.message}),a.dirty()),null!==i.maxLength&&t.data.length>i.maxLength.value&&((0,r.addIssueToContext)(t,{code:s.ZodIssueCode.too_big,maximum:i.maxLength.value,type:"array",inclusive:!0,exact:!1,message:i.maxLength.message}),a.dirty()),t.common.async)return Promise.all([...t.data].map(((e,a)=>i.type._parseAsync(new o(t,e,t.path,a))))).then((e=>r.ParseStatus.mergeArray(a,e)));const c=[...t.data].map(((e,a)=>i.type._parseSync(new o(t,e,t.path,a))));return r.ParseStatus.mergeArray(a,c)}get element(){return this._def.type}min(e,t){return new N({...this._def,minLength:{value:e,message:a.errorUtil.toString(t)}})}max(e,t){return new N({...this._def,maxLength:{value:e,message:a.errorUtil.toString(t)}})}length(e,t){return new N({...this._def,exactLength:{value:e,message:a.errorUtil.toString(t)}})}nonempty(e){return this.min(1,e)}}function j(e){if(e instanceof O){const t={};for(const a in e.shape){const r=e.shape[a];t[a]=X.create(j(r))}return new O({...e._def,shape:()=>t})}return e instanceof N?new N({...e._def,type:j(e.element)}):e instanceof X?X.create(j(e.unwrap())):e instanceof Y?Y.create(j(e.unwrap())):e instanceof L?L.create(e.items.map((e=>j(e)))):e}e.ZodArray=N,N.create=(e,t)=>new N({type:e,minLength:null,maxLength:null,exactLength:null,typeName:se.ZodArray,...c(t)});class O extends d{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=n.util.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==n.ZodParsedType.object){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.object,received:t.parsedType}),r.INVALID}const{status:t,ctx:a}=this._processInputParams(e),{shape:i,keys:c}=this._getCached(),d=[];if(!(this._def.catchall instanceof _&&"strip"===this._def.unknownKeys))for(const e in a.data)c.includes(e)||d.push(e);const u=[];for(const e of c){const t=i[e],r=a.data[e];u.push({key:{status:"valid",value:e},value:t._parse(new o(a,r,a.path,e)),alwaysSet:e in a.data})}if(this._def.catchall instanceof _){const e=this._def.unknownKeys;if("passthrough"===e)for(const e of d)u.push({key:{status:"valid",value:e},value:{status:"valid",value:a.data[e]}});else if("strict"===e)d.length>0&&((0,r.addIssueToContext)(a,{code:s.ZodIssueCode.unrecognized_keys,keys:d}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of d){const r=a.data[t];u.push({key:{status:"valid",value:t},value:e._parse(new o(a,r,a.path,t)),alwaysSet:t in a.data})}}return a.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of u){const a=await t.key;e.push({key:a,value:await t.value,alwaysSet:t.alwaysSet})}return e})).then((e=>r.ParseStatus.mergeObjectSync(t,e))):r.ParseStatus.mergeObjectSync(t,u)}get shape(){return this._def.shape()}strict(e){return a.errorUtil.errToObj,new O({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,r)=>{var n,s,o,i;const c=null!==(o=null===(s=(n=this._def).errorMap)||void 0===s?void 0:s.call(n,t,r).message)&&void 0!==o?o:r.defaultError;return"unrecognized_keys"===t.code?{message:null!==(i=a.errorUtil.errToObj(e).message)&&void 0!==i?i:c}:{message:c}}}:{}})}strip(){return new O({...this._def,unknownKeys:"strip"})}passthrough(){return new O({...this._def,unknownKeys:"passthrough"})}extend(e){return new O({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new O({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:se.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new O({...this._def,catchall:e})}pick(e){const t={};return n.util.objectKeys(e).forEach((a=>{e[a]&&this.shape[a]&&(t[a]=this.shape[a])})),new O({...this._def,shape:()=>t})}omit(e){const t={};return n.util.objectKeys(this.shape).forEach((a=>{e[a]||(t[a]=this.shape[a])})),new O({...this._def,shape:()=>t})}deepPartial(){return j(this)}partial(e){const t={};return n.util.objectKeys(this.shape).forEach((a=>{const r=this.shape[a];e&&!e[a]?t[a]=r:t[a]=r.optional()})),new O({...this._def,shape:()=>t})}required(e){const t={};return n.util.objectKeys(this.shape).forEach((a=>{if(e&&!e[a])t[a]=this.shape[a];else{let e=this.shape[a];for(;e instanceof X;)e=e._def.innerType;t[a]=e}})),new O({...this._def,shape:()=>t})}keyof(){return K(n.util.objectKeys(this.shape))}}e.ZodObject=O,O.create=(e,t)=>new O({shape:()=>e,unknownKeys:"strip",catchall:_.create(),typeName:se.ZodObject,...c(t)}),O.strictCreate=(e,t)=>new O({shape:()=>e,unknownKeys:"strict",catchall:_.create(),typeName:se.ZodObject,...c(t)}),O.lazycreate=(e,t)=>new O({shape:e,unknownKeys:"strip",catchall:_.create(),typeName:se.ZodObject,...c(t)});class M extends d{_parse(e){const{ctx:t}=this._processInputParams(e),a=this._def.options;if(t.common.async)return Promise.all(a.map((async e=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const a of e)if("dirty"===a.result.status)return t.common.issues.push(...a.ctx.common.issues),a.result;const a=e.map((e=>new s.ZodError(e.ctx.common.issues)));return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_union,unionErrors:a}),r.INVALID}));{let e;const n=[];for(const r of a){const a={...t,common:{...t.common,issues:[]},parent:null},s=r._parseSync({data:t.data,path:t.path,parent:a});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:a}),a.common.issues.length&&n.push(a.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const o=n.map((e=>new s.ZodError(e)));return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_union,unionErrors:o}),r.INVALID}}get options(){return this._def.options}}e.ZodUnion=M,M.create=(e,t)=>new M({options:e,typeName:se.ZodUnion,...c(t)});const A=e=>e instanceof V?A(e.schema):e instanceof G?A(e.innerType()):e instanceof $?[e.value]:e instanceof H?e.options:e instanceof W?Object.keys(e.enum):e instanceof Q?A(e._def.innerType):e instanceof I?[void 0]:e instanceof k?[null]:null;class B extends d{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==n.ZodParsedType.object)return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.object,received:t.parsedType}),r.INVALID;const a=this.discriminator,o=t.data[a],i=this.optionsMap.get(o);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):((0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[a]}),r.INVALID)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,a){const r=new Map;for(const a of t){const t=A(a.shape[e]);if(!t)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const n of t){if(r.has(n))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);r.set(n,a)}}return new B({typeName:se.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...c(a)})}}function z(e,t){const a=(0,n.getParsedType)(e),r=(0,n.getParsedType)(t);if(e===t)return{valid:!0,data:e};if(a===n.ZodParsedType.object&&r===n.ZodParsedType.object){const a=n.util.objectKeys(t),r=n.util.objectKeys(e).filter((e=>-1!==a.indexOf(e))),s={...e,...t};for(const a of r){const r=z(e[a],t[a]);if(!r.valid)return{valid:!1};s[a]=r.data}return{valid:!0,data:s}}if(a===n.ZodParsedType.array&&r===n.ZodParsedType.array){if(e.length!==t.length)return{valid:!1};const a=[];for(let r=0;r<e.length;r++){const n=z(e[r],t[r]);if(!n.valid)return{valid:!1};a.push(n.data)}return{valid:!0,data:a}}return a===n.ZodParsedType.date&&r===n.ZodParsedType.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}e.ZodDiscriminatedUnion=B;class D extends d{_parse(e){const{status:t,ctx:a}=this._processInputParams(e),n=(e,n)=>{if((0,r.isAborted)(e)||(0,r.isAborted)(n))return r.INVALID;const o=z(e.value,n.value);return o.valid?(((0,r.isDirty)(e)||(0,r.isDirty)(n))&&t.dirty(),{status:t.value,value:o.data}):((0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_intersection_types}),r.INVALID)};return a.common.async?Promise.all([this._def.left._parseAsync({data:a.data,path:a.path,parent:a}),this._def.right._parseAsync({data:a.data,path:a.path,parent:a})]).then((([e,t])=>n(e,t))):n(this._def.left._parseSync({data:a.data,path:a.path,parent:a}),this._def.right._parseSync({data:a.data,path:a.path,parent:a}))}}e.ZodIntersection=D,D.create=(e,t,a)=>new D({left:e,right:t,typeName:se.ZodIntersection,...c(a)});class L extends d{_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==n.ZodParsedType.array)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.array,received:a.parsedType}),r.INVALID;if(a.data.length<this._def.items.length)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),r.INVALID;!this._def.rest&&a.data.length>this._def.items.length&&((0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const i=[...a.data].map(((e,t)=>{const r=this._def.items[t]||this._def.rest;return r?r._parse(new o(a,e,a.path,t)):null})).filter((e=>!!e));return a.common.async?Promise.all(i).then((e=>r.ParseStatus.mergeArray(t,e))):r.ParseStatus.mergeArray(t,i)}get items(){return this._def.items}rest(e){return new L({...this._def,rest:e})}}e.ZodTuple=L,L.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new L({items:e,typeName:se.ZodTuple,rest:null,...c(t)})};class Z extends d{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==n.ZodParsedType.object)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.object,received:a.parsedType}),r.INVALID;const i=[],c=this._def.keyType,d=this._def.valueType;for(const e in a.data)i.push({key:c._parse(new o(a,e,a.path,e)),value:d._parse(new o(a,a.data[e],a.path,e))});return a.common.async?r.ParseStatus.mergeObjectAsync(t,i):r.ParseStatus.mergeObjectSync(t,i)}get element(){return this._def.valueType}static create(e,t,a){return new Z(t instanceof d?{keyType:e,valueType:t,typeName:se.ZodRecord,...c(a)}:{keyType:S.create(),valueType:e,typeName:se.ZodRecord,...c(t)})}}e.ZodRecord=Z;class U extends d{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==n.ZodParsedType.map)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.map,received:a.parsedType}),r.INVALID;const i=this._def.keyType,c=this._def.valueType,d=[...a.data.entries()].map((([e,t],r)=>({key:i._parse(new o(a,e,a.path,[r,"key"])),value:c._parse(new o(a,t,a.path,[r,"value"]))})));if(a.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const a of d){const n=await a.key,s=await a.value;if("aborted"===n.status||"aborted"===s.status)return r.INVALID;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const a of d){const n=a.key,s=a.value;if("aborted"===n.status||"aborted"===s.status)return r.INVALID;"dirty"!==n.status&&"dirty"!==s.status||t.dirty(),e.set(n.value,s.value)}return{status:t.value,value:e}}}}e.ZodMap=U,U.create=(e,t,a)=>new U({valueType:t,keyType:e,typeName:se.ZodMap,...c(a)});class F extends d{_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.parsedType!==n.ZodParsedType.set)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.set,received:a.parsedType}),r.INVALID;const i=this._def;null!==i.minSize&&a.data.size<i.minSize.value&&((0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_small,minimum:i.minSize.value,type:"set",inclusive:!0,exact:!1,message:i.minSize.message}),t.dirty()),null!==i.maxSize&&a.data.size>i.maxSize.value&&((0,r.addIssueToContext)(a,{code:s.ZodIssueCode.too_big,maximum:i.maxSize.value,type:"set",inclusive:!0,exact:!1,message:i.maxSize.message}),t.dirty());const c=this._def.valueType;function d(e){const a=new Set;for(const n of e){if("aborted"===n.status)return r.INVALID;"dirty"===n.status&&t.dirty(),a.add(n.value)}return{status:t.value,value:a}}const u=[...a.data.values()].map(((e,t)=>c._parse(new o(a,e,a.path,t))));return a.common.async?Promise.all(u).then((e=>d(e))):d(u)}min(e,t){return new F({...this._def,minSize:{value:e,message:a.errorUtil.toString(t)}})}max(e,t){return new F({...this._def,maxSize:{value:e,message:a.errorUtil.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}e.ZodSet=F,F.create=(e,t)=>new F({valueType:e,minSize:null,maxSize:null,typeName:se.ZodSet,...c(t)});class q extends d{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:a}=this._processInputParams(e);if(a.parsedType!==n.ZodParsedType.function)return(0,r.addIssueToContext)(a,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.function,received:a.parsedType}),r.INVALID;function o(e,n){return(0,r.makeIssue)({data:e,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,(0,t.getErrorMap)(),t.defaultErrorMap].filter((e=>!!e)),issueData:{code:s.ZodIssueCode.invalid_arguments,argumentsError:n}})}function i(e,n){return(0,r.makeIssue)({data:e,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,(0,t.getErrorMap)(),t.defaultErrorMap].filter((e=>!!e)),issueData:{code:s.ZodIssueCode.invalid_return_type,returnTypeError:n}})}const c={errorMap:a.common.contextualErrorMap},d=a.data;if(this._def.returns instanceof J){const e=this;return(0,r.OK)((async function(...t){const a=new s.ZodError([]),r=await e._def.args.parseAsync(t,c).catch((e=>{throw a.addIssue(o(t,e)),a})),n=await Reflect.apply(d,this,r),u=await e._def.returns._def.type.parseAsync(n,c).catch((e=>{throw a.addIssue(i(n,e)),a}));return u}))}{const e=this;return(0,r.OK)((function(...t){const a=e._def.args.safeParse(t,c);if(!a.success)throw new s.ZodError([o(t,a.error)]);const r=Reflect.apply(d,this,a.data),n=e._def.returns.safeParse(r,c);if(!n.success)throw new s.ZodError([i(r,n.error)]);return n.data}))}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new q({...this._def,args:L.create(e).rest(E.create())})}returns(e){return new q({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,a){return new q({args:e||L.create([]).rest(E.create()),returns:t||E.create(),typeName:se.ZodFunction,...c(a)})}}e.ZodFunction=q;class V extends d{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}e.ZodLazy=V,V.create=(e,t)=>new V({getter:e,typeName:se.ZodLazy,...c(t)});class $ extends d{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{received:t.data,code:s.ZodIssueCode.invalid_literal,expected:this._def.value}),r.INVALID}return{status:"valid",value:e.data}}get value(){return this._def.value}}function K(e,t){return new H({values:e,typeName:se.ZodEnum,...c(t)})}e.ZodLiteral=$,$.create=(e,t)=>new $({value:e,typeName:se.ZodLiteral,...c(t)});class H extends d{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),a=this._def.values;return(0,r.addIssueToContext)(t,{expected:n.util.joinValues(a),received:t.parsedType,code:s.ZodIssueCode.invalid_type}),r.INVALID}if(-1===this._def.values.indexOf(e.data)){const t=this._getOrReturnCtx(e),a=this._def.values;return(0,r.addIssueToContext)(t,{received:t.data,code:s.ZodIssueCode.invalid_enum_value,options:a}),r.INVALID}return(0,r.OK)(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e){return H.create(e)}exclude(e){return H.create(this.options.filter((t=>!e.includes(t))))}}e.ZodEnum=H,H.create=K;class W extends d{_parse(e){const t=n.util.getValidEnumValues(this._def.values),a=this._getOrReturnCtx(e);if(a.parsedType!==n.ZodParsedType.string&&a.parsedType!==n.ZodParsedType.number){const e=n.util.objectValues(t);return(0,r.addIssueToContext)(a,{expected:n.util.joinValues(e),received:a.parsedType,code:s.ZodIssueCode.invalid_type}),r.INVALID}if(-1===t.indexOf(e.data)){const e=n.util.objectValues(t);return(0,r.addIssueToContext)(a,{received:a.data,code:s.ZodIssueCode.invalid_enum_value,options:e}),r.INVALID}return(0,r.OK)(e.data)}get enum(){return this._def.values}}e.ZodNativeEnum=W,W.create=(e,t)=>new W({values:e,typeName:se.ZodNativeEnum,...c(t)});class J extends d{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==n.ZodParsedType.promise&&!1===t.common.async)return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.promise,received:t.parsedType}),r.INVALID;const a=t.parsedType===n.ZodParsedType.promise?t.data:Promise.resolve(t.data);return(0,r.OK)(a.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}e.ZodPromise=J,J.create=(e,t)=>new J({type:e,typeName:se.ZodPromise,...c(t)});class G extends d{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===se.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:a}=this._processInputParams(e),s=this._def.effect||null,o={addIssue:e=>{(0,r.addIssueToContext)(a,e),e.fatal?t.abort():t.dirty()},get path(){return a.path}};if(o.addIssue=o.addIssue.bind(o),"preprocess"===s.type){const e=s.transform(a.data,o);return a.common.issues.length?{status:"dirty",value:a.data}:a.common.async?Promise.resolve(e).then((e=>this._def.schema._parseAsync({data:e,path:a.path,parent:a}))):this._def.schema._parseSync({data:e,path:a.path,parent:a})}if("refinement"===s.type){const e=e=>{const t=s.refinement(e,o);if(a.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===a.common.async){const n=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===n.status?r.INVALID:("dirty"===n.status&&t.dirty(),e(n.value),{status:t.value,value:n.value})}return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then((a=>"aborted"===a.status?r.INVALID:("dirty"===a.status&&t.dirty(),e(a.value).then((()=>({status:t.value,value:a.value}))))))}if("transform"===s.type){if(!1===a.common.async){const e=this._def.schema._parseSync({data:a.data,path:a.path,parent:a});if(!(0,r.isValid)(e))return e;const n=s.transform(e.value,o);if(n instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:n}}return this._def.schema._parseAsync({data:a.data,path:a.path,parent:a}).then((e=>(0,r.isValid)(e)?Promise.resolve(s.transform(e.value,o)).then((e=>({status:t.value,value:e}))):e))}n.util.assertNever(s)}}e.ZodEffects=G,e.ZodTransformer=G,G.create=(e,t,a)=>new G({schema:e,typeName:se.ZodEffects,effect:t,...c(a)}),G.createWithPreprocess=(e,t,a)=>new G({schema:t,effect:{type:"preprocess",transform:e},typeName:se.ZodEffects,...c(a)});class X extends d{_parse(e){return this._getType(e)===n.ZodParsedType.undefined?(0,r.OK)(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e.ZodOptional=X,X.create=(e,t)=>new X({innerType:e,typeName:se.ZodOptional,...c(t)});class Y extends d{_parse(e){return this._getType(e)===n.ZodParsedType.null?(0,r.OK)(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e.ZodNullable=Y,Y.create=(e,t)=>new Y({innerType:e,typeName:se.ZodNullable,...c(t)});class Q extends d{_parse(e){const{ctx:t}=this._processInputParams(e);let a=t.data;return t.parsedType===n.ZodParsedType.undefined&&(a=this._def.defaultValue()),this._def.innerType._parse({data:a,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}e.ZodDefault=Q,Q.create=(e,t)=>new Q({innerType:e,typeName:se.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...c(t)});class ee extends d{_parse(e){const{ctx:t}=this._processInputParams(e),a={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:a.data,path:a.path,parent:{...a}});return(0,r.isAsync)(n)?n.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new s.ZodError(a.common.issues)},input:a.data})}))):{status:"valid",value:"valid"===n.status?n.value:this._def.catchValue({get error(){return new s.ZodError(a.common.issues)},input:a.data})}}removeCatch(){return this._def.innerType}}e.ZodCatch=ee,ee.create=(e,t)=>new ee({innerType:e,typeName:se.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...c(t)});class te extends d{_parse(e){if(this._getType(e)!==n.ZodParsedType.nan){const t=this._getOrReturnCtx(e);return(0,r.addIssueToContext)(t,{code:s.ZodIssueCode.invalid_type,expected:n.ZodParsedType.nan,received:t.parsedType}),r.INVALID}return{status:"valid",value:e.data}}}e.ZodNaN=te,te.create=e=>new te({typeName:se.ZodNaN,...c(e)}),e.BRAND=Symbol("zod_brand");class ae extends d{_parse(e){const{ctx:t}=this._processInputParams(e),a=t.data;return this._def.type._parse({data:a,path:t.path,parent:t})}unwrap(){return this._def.type}}e.ZodBranded=ae;class re extends d{_parse(e){const{status:t,ctx:a}=this._processInputParams(e);if(a.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?r.INVALID:"dirty"===e.status?(t.dirty(),(0,r.DIRTY)(e.value)):this._def.out._parseAsync({data:e.value,path:a.path,parent:a})})()}{const e=this._def.in._parseSync({data:a.data,path:a.path,parent:a});return"aborted"===e.status?r.INVALID:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:a.path,parent:a})}}static create(e,t){return new re({in:e,out:t,typeName:se.ZodPipeline})}}e.ZodPipeline=re;class ne extends d{_parse(e){const t=this._def.innerType._parse(e);return(0,r.isValid)(t)&&(t.value=Object.freeze(t.value)),t}}e.ZodReadonly=ne,ne.create=(e,t)=>new ne({innerType:e,typeName:se.ZodReadonly,...c(t)});var se;e.custom=(e,t={},a)=>e?R.create().superRefine(((r,n)=>{var s,o;if(!e(r)){const e="function"==typeof t?t(r):"string"==typeof t?{message:t}:t,i=null===(o=null!==(s=e.fatal)&&void 0!==s?s:a)||void 0===o||o,c="string"==typeof e?{message:e}:e;n.addIssue({code:"custom",...c,fatal:i})}})):R.create(),e.late={object:O.lazycreate},function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(se=e.ZodFirstPartyTypeKind||(e.ZodFirstPartyTypeKind={}));e.instanceof=(t,a={message:`Input not instance of ${t.name}`})=>(0,e.custom)((e=>e instanceof t),a);const oe=S.create;e.string=oe;const ie=w.create;e.number=ie;const ce=te.create;e.nan=ce;const de=C.create;e.bigint=de;const ue=b.create;e.boolean=ue;const le=x.create;e.date=le;const he=P.create;e.symbol=he;const pe=I.create;e.undefined=pe;const me=k.create;e.null=me;const fe=R.create;e.any=fe;const ge=E.create;e.unknown=ge;const ye=_.create;e.never=ye;const Se=T.create;e.void=Se;const ve=N.create;e.array=ve;const we=O.create;e.object=we;const Ce=O.strictCreate;e.strictObject=Ce;const be=M.create;e.union=be;const xe=B.create;e.discriminatedUnion=xe;const Pe=D.create;e.intersection=Pe;const Ie=L.create;e.tuple=Ie;const ke=Z.create;e.record=ke;const Re=U.create;e.map=Re;const Ee=F.create;e.set=Ee;const _e=q.create;e.function=_e;const Te=V.create;e.lazy=Te;const Ne=$.create;e.literal=Ne;const je=H.create;e.enum=je;const Oe=W.create;e.nativeEnum=Oe;const Me=J.create;e.promise=Me;const Ae=G.create;e.effect=Ae,e.transformer=Ae;const Be=X.create;e.optional=Be;const ze=Y.create;e.nullable=ze;const De=G.createWithPreprocess;e.preprocess=De;const Le=re.create;e.pipeline=Le;e.ostring=()=>oe().optional();e.onumber=()=>ie().optional();e.oboolean=()=>ue().optional(),e.coerce={string:e=>S.create({...e,coerce:!0}),number:e=>w.create({...e,coerce:!0}),boolean:e=>b.create({...e,coerce:!0}),bigint:e=>C.create({...e,coerce:!0}),date:e=>x.create({...e,coerce:!0})},e.NEVER=r.INVALID}(Rn),function(t){var a=e&&e.__createBinding||(Object.create?function(e,t,a,r){void 0===r&&(r=a),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[a]}})}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]}),r=e&&e.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),r(pn,t),r(Pn,t),r(In,t),r(fn,t),r(Rn,t),r(gn,t)}(hn),function(t){var a=e&&e.__createBinding||(Object.create?function(e,t,a,r){void 0===r&&(r=a),Object.defineProperty(e,r,{enumerable:!0,get:function(){return t[a]}})}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]}),r=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),n=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&a(t,e,n);return r(t,e),t},s=e&&e.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||a(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.z=void 0;const o=n(hn);t.z=o,s(hn,t),t.default=o}(ln);var _n={},Tn=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(_n,"__esModule",{value:!0}),_n.Permissions=_n.PermissionsCommandSchema=void 0;const Nn=Tn(ln);var jn;_n.PermissionsCommandSchema=Nn.default.lazy((()=>jn.SetPermissionSchema)),function(e){e.PermissionDescriptorSchema=Nn.default.lazy((()=>Nn.default.object({name:Nn.default.string()})))}(jn||(_n.Permissions=jn={})),function(e){e.PermissionStateSchema=Nn.default.lazy((()=>Nn.default.enum(["granted","denied","prompt"])))}(jn||(_n.Permissions=jn={})),function(e){e.SetPermissionSchema=Nn.default.lazy((()=>Nn.default.object({method:Nn.default.literal("permissions.setPermission"),params:e.SetPermissionParametersSchema})))}(jn||(_n.Permissions=jn={})),function(e){e.SetPermissionParametersSchema=Nn.default.lazy((()=>Nn.default.object({descriptor:e.PermissionDescriptorSchema,state:e.PermissionStateSchema,origin:Nn.default.string()})))}(jn||(_n.Permissions=jn={}));var On={};!function(t){var a=e&&e.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.Input=t.InputCommandSchema=t.Log=t.LogEventSchema=t.Storage=t.StorageResultSchema=t.StorageCommandSchema=t.Script=t.ScriptResultSchema=t.ScriptCommandSchema=t.ScriptEventSchema=t.Network=t.NetworkResultSchema=t.NetworkEventSchema=t.NetworkCommandSchema=t.BrowsingContext=t.BrowsingContextResultSchema=t.BrowsingContextEventSchema=t.BrowsingContextCommandSchema=t.Browser=t.BrowserResultSchema=t.BrowserCommandSchema=t.SessionResultSchema=t.Session=t.SessionCommandSchema=t.ErrorCodeSchema=t.JsUintSchema=t.JsIntSchema=t.ExtensibleSchema=t.EmptyResultSchema=t.ErrorResponseSchema=t.MessageSchema=t.EmptyParamsSchema=t.ResultDataSchema=t.CommandDataSchema=t.EventDataSchema=t.CommandResponseSchema=t.CommandSchema=t.EventSchema=void 0;const r=a(ln);var n,s,o,i,c,d,u,l;t.EventSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("event")}).and(t.EventDataSchema).and(t.ExtensibleSchema))),t.CommandSchema=r.default.lazy((()=>r.default.object({id:t.JsUintSchema}).and(t.CommandDataSchema).and(t.ExtensibleSchema))),t.CommandResponseSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("success"),id:t.JsUintSchema,result:t.ResultDataSchema}).and(t.ExtensibleSchema))),t.EventDataSchema=r.default.lazy((()=>r.default.union([t.BrowsingContextEventSchema,t.LogEventSchema,t.NetworkEventSchema,t.ScriptEventSchema]))),t.CommandDataSchema=r.default.lazy((()=>r.default.union([t.BrowserCommandSchema,t.BrowsingContextCommandSchema,t.InputCommandSchema,t.NetworkCommandSchema,t.ScriptCommandSchema,t.SessionCommandSchema,t.StorageCommandSchema]))),t.ResultDataSchema=r.default.lazy((()=>r.default.union([t.BrowsingContextResultSchema,t.EmptyResultSchema,t.NetworkResultSchema,t.ScriptResultSchema,t.SessionResultSchema,t.StorageResultSchema]))),t.EmptyParamsSchema=r.default.lazy((()=>t.ExtensibleSchema)),t.MessageSchema=r.default.lazy((()=>r.default.union([t.CommandResponseSchema,t.ErrorResponseSchema,t.EventSchema]))),t.ErrorResponseSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("error"),id:r.default.union([t.JsUintSchema,r.default.null()]),error:t.ErrorCodeSchema,message:r.default.string(),stacktrace:r.default.string().optional()}).and(t.ExtensibleSchema))),t.EmptyResultSchema=r.default.lazy((()=>t.ExtensibleSchema)),t.ExtensibleSchema=r.default.lazy((()=>r.default.record(r.default.string(),r.default.any()))),t.JsIntSchema=r.default.lazy((()=>r.default.number().int().gte(-9007199254740991).lte(9007199254740991))),t.JsUintSchema=r.default.lazy((()=>r.default.number().int().nonnegative().gte(0).lte(9007199254740991))),t.ErrorCodeSchema=r.default.lazy((()=>r.default.enum(["invalid argument","invalid session id","move target out of bounds","no such alert","no such element","no such frame","no such handle","no such history entry","no such intercept","no such node","no such request","no such script","no such storage partition","no such user context","session not created","unable to capture screen","unable to close browser","unable to set cookie","unable to set file input","underspecified storage partition","unknown command","unknown error","unsupported operation"]))),t.SessionCommandSchema=r.default.lazy((()=>r.default.union([n.EndSchema,n.NewSchema,n.StatusSchema,n.SubscribeSchema,n.UnsubscribeSchema]))),function(e){e.ProxyConfigurationSchema=r.default.lazy((()=>r.default.union([e.AutodetectProxyConfigurationSchema,e.DirectProxyConfigurationSchema,e.ManualProxyConfigurationSchema,e.PacProxyConfigurationSchema,e.SystemProxyConfigurationSchema,r.default.object({})])))}(n||(t.Session=n={})),t.SessionResultSchema=r.default.lazy((()=>r.default.union([n.NewResultSchema,n.StatusResultSchema]))),function(e){e.CapabilitiesRequestSchema=r.default.lazy((()=>r.default.object({alwaysMatch:e.CapabilityRequestSchema.optional(),firstMatch:r.default.array(e.CapabilityRequestSchema).optional()})))}(n||(t.Session=n={})),function(e){e.CapabilityRequestSchema=r.default.lazy((()=>r.default.object({acceptInsecureCerts:r.default.boolean().optional(),browserName:r.default.string().optional(),browserVersion:r.default.string().optional(),platformName:r.default.string().optional(),proxy:e.ProxyConfigurationSchema.optional(),webSocketUrl:r.default.boolean().optional()}).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.AutodetectProxyConfigurationSchema=r.default.lazy((()=>r.default.object({proxyType:r.default.literal("autodetect")}).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.DirectProxyConfigurationSchema=r.default.lazy((()=>r.default.object({proxyType:r.default.literal("direct")}).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.ManualProxyConfigurationSchema=r.default.lazy((()=>r.default.object({proxyType:r.default.literal("manual"),ftpProxy:r.default.string().optional(),httpProxy:r.default.string().optional(),sslProxy:r.default.string().optional()}).and(e.SocksProxyConfigurationSchema.or(r.default.object({}))).and(r.default.object({noProxy:r.default.array(r.default.string()).optional()})).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.SocksProxyConfigurationSchema=r.default.lazy((()=>r.default.object({socksProxy:r.default.string(),socksVersion:r.default.number().int().nonnegative().gte(0).lte(255)})))}(n||(t.Session=n={})),function(e){e.PacProxyConfigurationSchema=r.default.lazy((()=>r.default.object({proxyType:r.default.literal("pac"),proxyAutoconfigUrl:r.default.string()}).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.SystemProxyConfigurationSchema=r.default.lazy((()=>r.default.object({proxyType:r.default.literal("system")}).and(t.ExtensibleSchema)))}(n||(t.Session=n={})),function(e){e.SubscriptionRequestSchema=r.default.lazy((()=>r.default.object({events:r.default.array(r.default.string()),contexts:r.default.array(o.BrowsingContextSchema).optional()})))}(n||(t.Session=n={})),function(e){e.StatusSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("session.status"),params:t.EmptyParamsSchema})))}(n||(t.Session=n={})),function(e){e.StatusResultSchema=r.default.lazy((()=>r.default.object({ready:r.default.boolean(),message:r.default.string()})))}(n||(t.Session=n={})),function(e){e.NewSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("session.new"),params:e.NewParametersSchema})))}(n||(t.Session=n={})),function(e){e.NewParametersSchema=r.default.lazy((()=>r.default.object({capabilities:e.CapabilitiesRequestSchema})))}(n||(t.Session=n={})),function(e){e.NewResultSchema=r.default.lazy((()=>r.default.object({sessionId:r.default.string(),capabilities:r.default.object({acceptInsecureCerts:r.default.boolean(),browserName:r.default.string(),browserVersion:r.default.string(),platformName:r.default.string(),setWindowRect:r.default.boolean(),proxy:e.ProxyConfigurationSchema.optional(),webSocketUrl:r.default.string().optional()}).and(t.ExtensibleSchema)})))}(n||(t.Session=n={})),function(e){e.EndSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("session.end"),params:t.EmptyParamsSchema})))}(n||(t.Session=n={})),function(e){e.SubscribeSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("session.subscribe"),params:e.SubscriptionRequestSchema})))}(n||(t.Session=n={})),function(e){e.UnsubscribeSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("session.unsubscribe"),params:e.SubscriptionRequestSchema})))}(n||(t.Session=n={})),t.BrowserCommandSchema=r.default.lazy((()=>r.default.union([s.CloseSchema,s.CreateUserContextSchema,s.GetUserContextsSchema,s.RemoveUserContextSchema]))),t.BrowserResultSchema=r.default.lazy((()=>r.default.union([s.CreateUserContextResultSchema,s.GetUserContextsResultSchema]))),function(e){e.UserContextSchema=r.default.lazy((()=>r.default.string()))}(s||(t.Browser=s={})),function(e){e.UserContextInfoSchema=r.default.lazy((()=>r.default.object({userContext:e.UserContextSchema})))}(s||(t.Browser=s={})),function(e){e.CloseSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browser.close"),params:t.EmptyParamsSchema})))}(s||(t.Browser=s={})),function(e){e.CreateUserContextSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browser.createUserContext"),params:t.EmptyParamsSchema})))}(s||(t.Browser=s={})),function(e){e.CreateUserContextResultSchema=r.default.lazy((()=>e.UserContextInfoSchema))}(s||(t.Browser=s={})),function(e){e.GetUserContextsSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browser.getUserContexts"),params:t.EmptyParamsSchema})))}(s||(t.Browser=s={})),function(e){e.GetUserContextsResultSchema=r.default.lazy((()=>r.default.object({userContexts:r.default.array(e.UserContextInfoSchema).min(1)})))}(s||(t.Browser=s={})),function(e){e.RemoveUserContextSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browser.removeUserContext"),params:r.default.object({userContext:e.UserContextSchema})})))}(s||(t.Browser=s={})),t.BrowsingContextCommandSchema=r.default.lazy((()=>r.default.union([o.ActivateSchema,o.CaptureScreenshotSchema,o.CloseSchema,o.CreateSchema,o.GetTreeSchema,o.HandleUserPromptSchema,o.LocateNodesSchema,o.NavigateSchema,o.PrintSchema,o.ReloadSchema,o.SetViewportSchema,o.TraverseHistorySchema]))),t.BrowsingContextEventSchema=r.default.lazy((()=>r.default.union([o.ContextCreatedSchema,o.ContextDestroyedSchema,o.DomContentLoadedSchema,o.DownloadWillBeginSchema,o.FragmentNavigatedSchema,o.LoadSchema,o.NavigationAbortedSchema,o.NavigationFailedSchema,o.NavigationStartedSchema,o.UserPromptClosedSchema,o.UserPromptOpenedSchema]))),t.BrowsingContextResultSchema=r.default.lazy((()=>r.default.union([o.CaptureScreenshotResultSchema,o.CreateResultSchema,o.GetTreeResultSchema,o.LocateNodesResultSchema,o.NavigateResultSchema,o.PrintResultSchema,o.TraverseHistoryResultSchema]))),function(e){e.BrowsingContextSchema=r.default.lazy((()=>r.default.string()))}(o||(t.BrowsingContext=o={})),function(e){e.InfoListSchema=r.default.lazy((()=>r.default.array(e.InfoSchema)))}(o||(t.BrowsingContext=o={})),function(e){e.InfoSchema=r.default.lazy((()=>r.default.object({children:r.default.union([e.InfoListSchema,r.default.null()]),context:e.BrowsingContextSchema,url:r.default.string(),userContext:s.UserContextSchema,parent:r.default.union([e.BrowsingContextSchema,r.default.null()]).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.LocatorSchema=r.default.lazy((()=>r.default.union([e.CssLocatorSchema,e.InnerTextLocatorSchema,e.XPathLocatorSchema])))}(o||(t.BrowsingContext=o={})),function(e){e.CssLocatorSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("css"),value:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.InnerTextLocatorSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("innerText"),value:r.default.string(),ignoreCase:r.default.boolean().optional(),matchType:r.default.enum(["full","partial"]).optional(),maxDepth:t.JsUintSchema.optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.XPathLocatorSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("xpath"),value:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigationSchema=r.default.lazy((()=>r.default.string()))}(o||(t.BrowsingContext=o={})),function(e){e.NavigationInfoSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,navigation:r.default.union([e.NavigationSchema,r.default.null()]),timestamp:t.JsUintSchema,url:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.ReadinessStateSchema=r.default.lazy((()=>r.default.enum(["none","interactive","complete"])))}(o||(t.BrowsingContext=o={})),function(e){e.ActivateSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.activate"),params:e.ActivateParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.ActivateParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.CaptureScreenshotParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,origin:r.default.enum(["viewport","document"]).default("viewport").optional(),format:e.ImageFormatSchema.optional(),clip:e.ClipRectangleSchema.optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.CaptureScreenshotSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.captureScreenshot"),params:e.CaptureScreenshotParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.ImageFormatSchema=r.default.lazy((()=>r.default.object({type:r.default.string(),quality:r.default.number().gte(0).lte(1).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.ClipRectangleSchema=r.default.lazy((()=>r.default.union([e.BoxClipRectangleSchema,e.ElementClipRectangleSchema])))}(o||(t.BrowsingContext=o={})),function(e){e.ElementClipRectangleSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("element"),element:c.SharedReferenceSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.BoxClipRectangleSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("box"),x:r.default.number(),y:r.default.number(),width:r.default.number(),height:r.default.number()})))}(o||(t.BrowsingContext=o={})),function(e){e.CaptureScreenshotResultSchema=r.default.lazy((()=>r.default.object({data:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.CloseSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.close"),params:e.CloseParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.CloseParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,promptUnload:r.default.boolean().default(!1).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.CreateSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.create"),params:e.CreateParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.CreateTypeSchema=r.default.lazy((()=>r.default.enum(["tab","window"])))}(o||(t.BrowsingContext=o={})),function(e){e.CreateParametersSchema=r.default.lazy((()=>r.default.object({type:e.CreateTypeSchema,referenceContext:e.BrowsingContextSchema.optional(),background:r.default.boolean().default(!1).optional(),userContext:r.default.union([s.UserContextSchema,r.default.null()]).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.CreateResultSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.GetTreeSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.getTree"),params:e.GetTreeParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.GetTreeParametersSchema=r.default.lazy((()=>r.default.object({maxDepth:t.JsUintSchema.optional(),root:e.BrowsingContextSchema.optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.GetTreeResultSchema=r.default.lazy((()=>r.default.object({contexts:e.InfoListSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.HandleUserPromptSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.handleUserPrompt"),params:e.HandleUserPromptParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.HandleUserPromptParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,accept:r.default.boolean().optional(),userText:r.default.string().optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.LocateNodesParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,locator:e.LocatorSchema,maxNodeCount:t.JsUintSchema.gte(1).optional(),ownership:c.ResultOwnershipSchema.optional(),sandbox:r.default.string().optional(),serializationOptions:c.SerializationOptionsSchema.optional(),startNodes:r.default.array(c.SharedReferenceSchema).min(1).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.LocateNodesSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.locateNodes"),params:e.LocateNodesParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.LocateNodesResultSchema=r.default.lazy((()=>r.default.object({nodes:r.default.array(c.NodeRemoteValueSchema)})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigateSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.navigate"),params:e.NavigateParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigateParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,url:r.default.string(),wait:e.ReadinessStateSchema.optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigateResultSchema=r.default.lazy((()=>r.default.object({navigation:r.default.union([e.NavigationSchema,r.default.null()]),url:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.PrintSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.print"),params:e.PrintParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.PrintParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,background:r.default.boolean().default(!1).optional(),margin:e.PrintMarginParametersSchema.optional(),orientation:r.default.enum(["portrait","landscape"]).default("portrait").optional(),page:e.PrintPageParametersSchema.optional(),pageRanges:r.default.array(r.default.union([t.JsUintSchema,r.default.string()])).optional(),scale:r.default.number().gte(.1).lte(2).default(1).optional(),shrinkToFit:r.default.boolean().default(!0).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.PrintMarginParametersSchema=r.default.lazy((()=>r.default.object({bottom:r.default.number().gte(0).default(1).optional(),left:r.default.number().gte(0).default(1).optional(),right:r.default.number().gte(0).default(1).optional(),top:r.default.number().gte(0).default(1).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.PrintPageParametersSchema=r.default.lazy((()=>r.default.object({height:r.default.number().gte(.0352).default(27.94).optional(),width:r.default.number().gte(.0352).default(21.59).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.PrintResultSchema=r.default.lazy((()=>r.default.object({data:r.default.string()})))}(o||(t.BrowsingContext=o={})),function(e){e.ReloadSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.reload"),params:e.ReloadParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.ReloadParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,ignoreCache:r.default.boolean().optional(),wait:e.ReadinessStateSchema.optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.SetViewportSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.setViewport"),params:e.SetViewportParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.SetViewportParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,viewport:r.default.union([e.ViewportSchema,r.default.null()]).optional(),devicePixelRatio:r.default.union([r.default.number().gt(0),r.default.null()]).optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.ViewportSchema=r.default.lazy((()=>r.default.object({width:t.JsUintSchema,height:t.JsUintSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.TraverseHistorySchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.traverseHistory"),params:e.TraverseHistoryParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.TraverseHistoryParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,delta:t.JsIntSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.TraverseHistoryResultSchema=r.default.lazy((()=>r.default.object({})))}(o||(t.BrowsingContext=o={})),function(e){e.ContextCreatedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.contextCreated"),params:e.InfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.ContextDestroyedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.contextDestroyed"),params:e.InfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigationStartedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.navigationStarted"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.FragmentNavigatedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.fragmentNavigated"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.DomContentLoadedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.domContentLoaded"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.LoadSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.load"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.DownloadWillBeginSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.downloadWillBegin"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigationAbortedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.navigationAborted"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.NavigationFailedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.navigationFailed"),params:e.NavigationInfoSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.UserPromptClosedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.userPromptClosed"),params:e.UserPromptClosedParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.UserPromptClosedParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,accepted:r.default.boolean(),userText:r.default.string().optional()})))}(o||(t.BrowsingContext=o={})),function(e){e.UserPromptOpenedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("browsingContext.userPromptOpened"),params:e.UserPromptOpenedParametersSchema})))}(o||(t.BrowsingContext=o={})),function(e){e.UserPromptOpenedParametersSchema=r.default.lazy((()=>r.default.object({context:e.BrowsingContextSchema,type:r.default.enum(["alert","confirm","prompt","beforeunload"]),message:r.default.string(),defaultValue:r.default.string().optional()})))}(o||(t.BrowsingContext=o={})),t.NetworkCommandSchema=r.default.lazy((()=>r.default.union([i.AddInterceptSchema,i.ContinueRequestSchema,i.ContinueResponseSchema,i.ContinueWithAuthSchema,i.FailRequestSchema,i.ProvideResponseSchema,i.RemoveInterceptSchema]))),t.NetworkEventSchema=r.default.lazy((()=>r.default.union([i.AuthRequiredSchema,i.BeforeRequestSentSchema,i.FetchErrorSchema,i.ResponseCompletedSchema,i.ResponseStartedSchema]))),t.NetworkResultSchema=r.default.lazy((()=>i.AddInterceptResultSchema)),function(e){e.AuthChallengeSchema=r.default.lazy((()=>r.default.object({scheme:r.default.string(),realm:r.default.string()})))}(i||(t.Network=i={})),function(e){e.AuthCredentialsSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("password"),username:r.default.string(),password:r.default.string()})))}(i||(t.Network=i={})),function(e){e.BaseParametersSchema=r.default.lazy((()=>r.default.object({context:r.default.union([o.BrowsingContextSchema,r.default.null()]),isBlocked:r.default.boolean(),navigation:r.default.union([o.NavigationSchema,r.default.null()]),redirectCount:t.JsUintSchema,request:e.RequestDataSchema,timestamp:t.JsUintSchema,intercepts:r.default.array(e.InterceptSchema).min(1).optional()})))}(i||(t.Network=i={})),function(e){e.BytesValueSchema=r.default.lazy((()=>r.default.union([e.StringValueSchema,e.Base64ValueSchema])))}(i||(t.Network=i={})),function(e){e.StringValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("string"),value:r.default.string()})))}(i||(t.Network=i={})),function(e){e.Base64ValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("base64"),value:r.default.string()})))}(i||(t.Network=i={})),function(e){e.SameSiteSchema=r.default.lazy((()=>r.default.enum(["strict","lax","none"])))}(i||(t.Network=i={})),function(e){e.CookieSchema=r.default.lazy((()=>r.default.object({name:r.default.string(),value:e.BytesValueSchema,domain:r.default.string(),path:r.default.string(),size:t.JsUintSchema,httpOnly:r.default.boolean(),secure:r.default.boolean(),sameSite:e.SameSiteSchema,expiry:t.JsUintSchema.optional()}).and(t.ExtensibleSchema)))}(i||(t.Network=i={})),function(e){e.CookieHeaderSchema=r.default.lazy((()=>r.default.object({name:r.default.string(),value:e.BytesValueSchema})))}(i||(t.Network=i={})),function(e){e.FetchTimingInfoSchema=r.default.lazy((()=>r.default.object({timeOrigin:r.default.number(),requestTime:r.default.number(),redirectStart:r.default.number(),redirectEnd:r.default.number(),fetchStart:r.default.number(),dnsStart:r.default.number(),dnsEnd:r.default.number(),connectStart:r.default.number(),connectEnd:r.default.number(),tlsStart:r.default.number(),requestStart:r.default.number(),responseStart:r.default.number(),responseEnd:r.default.number()})))}(i||(t.Network=i={})),function(e){e.HeaderSchema=r.default.lazy((()=>r.default.object({name:r.default.string(),value:e.BytesValueSchema})))}(i||(t.Network=i={})),function(e){e.InitiatorSchema=r.default.lazy((()=>r.default.object({type:r.default.enum(["parser","script","preflight","other"]),columnNumber:t.JsUintSchema.optional(),lineNumber:t.JsUintSchema.optional(),stackTrace:c.StackTraceSchema.optional(),request:e.RequestSchema.optional()})))}(i||(t.Network=i={})),function(e){e.InterceptSchema=r.default.lazy((()=>r.default.string()))}(i||(t.Network=i={})),function(e){e.RequestSchema=r.default.lazy((()=>r.default.string()))}(i||(t.Network=i={})),function(e){e.RequestDataSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema,url:r.default.string(),method:r.default.string(),headers:r.default.array(e.HeaderSchema),cookies:r.default.array(e.CookieSchema),headersSize:t.JsUintSchema,bodySize:r.default.union([t.JsUintSchema,r.default.null()]),timings:e.FetchTimingInfoSchema})))}(i||(t.Network=i={})),function(e){e.ResponseContentSchema=r.default.lazy((()=>r.default.object({size:t.JsUintSchema})))}(i||(t.Network=i={})),function(e){e.ResponseDataSchema=r.default.lazy((()=>r.default.object({url:r.default.string(),protocol:r.default.string(),status:t.JsUintSchema,statusText:r.default.string(),fromCache:r.default.boolean(),headers:r.default.array(e.HeaderSchema),mimeType:r.default.string(),bytesReceived:t.JsUintSchema,headersSize:r.default.union([t.JsUintSchema,r.default.null()]),bodySize:r.default.union([t.JsUintSchema,r.default.null()]),content:e.ResponseContentSchema,authChallenge:e.AuthChallengeSchema.optional()})))}(i||(t.Network=i={})),function(e){e.SetCookieHeaderSchema=r.default.lazy((()=>r.default.object({name:r.default.string(),value:e.BytesValueSchema,domain:r.default.string().optional(),httpOnly:r.default.boolean().optional(),expiry:r.default.string().optional(),maxAge:t.JsIntSchema.optional(),path:r.default.string().optional(),sameSite:e.SameSiteSchema.optional(),secure:r.default.boolean().optional()})))}(i||(t.Network=i={})),function(e){e.UrlPatternSchema=r.default.lazy((()=>r.default.union([e.UrlPatternPatternSchema,e.UrlPatternStringSchema])))}(i||(t.Network=i={})),function(e){e.UrlPatternPatternSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pattern"),protocol:r.default.string().optional(),hostname:r.default.string().optional(),port:r.default.string().optional(),pathname:r.default.string().optional(),search:r.default.string().optional()})))}(i||(t.Network=i={})),function(e){e.UrlPatternStringSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("string"),pattern:r.default.string()})))}(i||(t.Network=i={})),function(e){e.AddInterceptParametersSchema=r.default.lazy((()=>r.default.object({phases:r.default.array(e.InterceptPhaseSchema).min(1),urlPatterns:r.default.array(e.UrlPatternSchema).optional()})))}(i||(t.Network=i={})),function(e){e.AddInterceptSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.addIntercept"),params:e.AddInterceptParametersSchema})))}(i||(t.Network=i={})),function(e){e.InterceptPhaseSchema=r.default.lazy((()=>r.default.enum(["beforeRequestSent","responseStarted","authRequired"])))}(i||(t.Network=i={})),function(e){e.AddInterceptResultSchema=r.default.lazy((()=>r.default.object({intercept:e.InterceptSchema})))}(i||(t.Network=i={})),function(e){e.ContinueRequestSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.continueRequest"),params:e.ContinueRequestParametersSchema})))}(i||(t.Network=i={})),function(e){e.ContinueRequestParametersSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema,body:e.BytesValueSchema.optional(),cookies:r.default.array(e.CookieHeaderSchema).optional(),headers:r.default.array(e.HeaderSchema).optional(),method:r.default.string().optional(),url:r.default.string().optional()})))}(i||(t.Network=i={})),function(e){e.ContinueResponseSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.continueResponse"),params:e.ContinueResponseParametersSchema})))}(i||(t.Network=i={})),function(e){e.ContinueResponseParametersSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema,cookies:r.default.array(e.SetCookieHeaderSchema).optional(),credentials:e.AuthCredentialsSchema.optional(),headers:r.default.array(e.HeaderSchema).optional(),reasonPhrase:r.default.string().optional(),statusCode:t.JsUintSchema.optional()})))}(i||(t.Network=i={})),function(e){e.ContinueWithAuthSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.continueWithAuth"),params:e.ContinueWithAuthParametersSchema})))}(i||(t.Network=i={})),function(e){e.ContinueWithAuthParametersSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema}).and(r.default.union([e.ContinueWithAuthCredentialsSchema,e.ContinueWithAuthNoCredentialsSchema]))))}(i||(t.Network=i={})),function(e){e.ContinueWithAuthCredentialsSchema=r.default.lazy((()=>r.default.object({action:r.default.literal("provideCredentials"),credentials:e.AuthCredentialsSchema})))}(i||(t.Network=i={})),function(e){e.ContinueWithAuthNoCredentialsSchema=r.default.lazy((()=>r.default.object({action:r.default.enum(["default","cancel"])})))}(i||(t.Network=i={})),function(e){e.FailRequestSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.failRequest"),params:e.FailRequestParametersSchema})))}(i||(t.Network=i={})),function(e){e.FailRequestParametersSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema})))}(i||(t.Network=i={})),function(e){e.ProvideResponseSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.provideResponse"),params:e.ProvideResponseParametersSchema})))}(i||(t.Network=i={})),function(e){e.ProvideResponseParametersSchema=r.default.lazy((()=>r.default.object({request:e.RequestSchema,body:e.BytesValueSchema.optional(),cookies:r.default.array(e.SetCookieHeaderSchema).optional(),headers:r.default.array(e.HeaderSchema).optional(),reasonPhrase:r.default.string().optional(),statusCode:t.JsUintSchema.optional()})))}(i||(t.Network=i={})),function(e){e.RemoveInterceptSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.removeIntercept"),params:e.RemoveInterceptParametersSchema})))}(i||(t.Network=i={})),function(e){e.RemoveInterceptParametersSchema=r.default.lazy((()=>r.default.object({intercept:e.InterceptSchema})))}(i||(t.Network=i={})),t.ScriptEventSchema=r.default.lazy((()=>r.default.union([c.MessageSchema,c.RealmCreatedSchema,c.RealmDestroyedSchema]))),function(e){e.AuthRequiredParametersSchema=r.default.lazy((()=>e.BaseParametersSchema.and(r.default.object({response:e.ResponseDataSchema}))))}(i||(t.Network=i={})),function(e){e.BeforeRequestSentParametersSchema=r.default.lazy((()=>e.BaseParametersSchema.and(r.default.object({initiator:e.InitiatorSchema}))))}(i||(t.Network=i={})),function(e){e.FetchErrorParametersSchema=r.default.lazy((()=>e.BaseParametersSchema.and(r.default.object({errorText:r.default.string()}))))}(i||(t.Network=i={})),function(e){e.ResponseCompletedParametersSchema=r.default.lazy((()=>e.BaseParametersSchema.and(r.default.object({response:e.ResponseDataSchema}))))}(i||(t.Network=i={})),function(e){e.ResponseStartedParametersSchema=r.default.lazy((()=>e.BaseParametersSchema.and(r.default.object({response:e.ResponseDataSchema}))))}(i||(t.Network=i={})),t.ScriptCommandSchema=r.default.lazy((()=>r.default.union([c.AddPreloadScriptSchema,c.CallFunctionSchema,c.DisownSchema,c.EvaluateSchema,c.GetRealmsSchema,c.RemovePreloadScriptSchema]))),t.ScriptResultSchema=r.default.lazy((()=>r.default.union([c.AddPreloadScriptResultSchema,c.EvaluateResultSchema,c.GetRealmsResultSchema]))),function(e){e.AuthRequiredSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.authRequired"),params:e.AuthRequiredParametersSchema})))}(i||(t.Network=i={})),function(e){e.BeforeRequestSentSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.beforeRequestSent"),params:e.BeforeRequestSentParametersSchema})))}(i||(t.Network=i={})),function(e){e.FetchErrorSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.fetchError"),params:e.FetchErrorParametersSchema})))}(i||(t.Network=i={})),function(e){e.ResponseCompletedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.responseCompleted"),params:e.ResponseCompletedParametersSchema})))}(i||(t.Network=i={})),function(e){e.ResponseStartedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("network.responseStarted"),params:e.ResponseStartedParametersSchema})))}(i||(t.Network=i={})),function(e){e.ChannelSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.EvaluateResultSuccessSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("success"),result:e.RemoteValueSchema,realm:e.RealmSchema})))}(c||(t.Script=c={})),function(e){e.ExceptionDetailsSchema=r.default.lazy((()=>r.default.object({columnNumber:t.JsUintSchema,exception:e.RemoteValueSchema,lineNumber:t.JsUintSchema,stackTrace:e.StackTraceSchema,text:r.default.string()})))}(c||(t.Script=c={})),function(e){e.ChannelValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("channel"),value:e.ChannelPropertiesSchema})))}(c||(t.Script=c={})),function(e){e.ChannelPropertiesSchema=r.default.lazy((()=>r.default.object({channel:e.ChannelSchema,serializationOptions:e.SerializationOptionsSchema.optional(),ownership:e.ResultOwnershipSchema.optional()})))}(c||(t.Script=c={})),function(e){e.EvaluateResultSchema=r.default.lazy((()=>r.default.union([e.EvaluateResultSuccessSchema,e.EvaluateResultExceptionSchema])))}(c||(t.Script=c={})),function(e){e.EvaluateResultExceptionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("exception"),exceptionDetails:e.ExceptionDetailsSchema,realm:e.RealmSchema})))}(c||(t.Script=c={})),function(e){e.HandleSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.InternalIdSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.ListLocalValueSchema=r.default.lazy((()=>r.default.array(e.LocalValueSchema)))}(c||(t.Script=c={})),function(e){e.LocalValueSchema=r.default.lazy((()=>r.default.union([e.RemoteReferenceSchema,e.PrimitiveProtocolValueSchema,e.ChannelValueSchema,e.ArrayLocalValueSchema,e.DateLocalValueSchema,e.MapLocalValueSchema,e.ObjectLocalValueSchema,e.RegExpLocalValueSchema,e.SetLocalValueSchema])))}(c||(t.Script=c={})),function(e){e.ArrayLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("array"),value:e.ListLocalValueSchema})))}(c||(t.Script=c={})),function(e){e.DateLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("date"),value:r.default.string()})))}(c||(t.Script=c={})),function(e){e.MappingLocalValueSchema=r.default.lazy((()=>r.default.array(r.default.tuple([r.default.union([e.LocalValueSchema,r.default.string()]),e.LocalValueSchema]))))}(c||(t.Script=c={})),function(e){e.MapLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("map"),value:e.MappingLocalValueSchema})))}(c||(t.Script=c={})),function(e){e.ObjectLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("object"),value:e.MappingLocalValueSchema})))}(c||(t.Script=c={})),function(e){e.RegExpValueSchema=r.default.lazy((()=>r.default.object({pattern:r.default.string(),flags:r.default.string().optional()})))}(c||(t.Script=c={})),function(e){e.RegExpLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("regexp"),value:e.RegExpValueSchema})))}(c||(t.Script=c={})),function(e){e.SetLocalValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("set"),value:e.ListLocalValueSchema})))}(c||(t.Script=c={})),function(e){e.PreloadScriptSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.RealmSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.PrimitiveProtocolValueSchema=r.default.lazy((()=>r.default.union([e.UndefinedValueSchema,e.NullValueSchema,e.StringValueSchema,e.NumberValueSchema,e.BooleanValueSchema,e.BigIntValueSchema])))}(c||(t.Script=c={})),function(e){e.UndefinedValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("undefined")})))}(c||(t.Script=c={})),function(e){e.NullValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("null")})))}(c||(t.Script=c={})),function(e){e.StringValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("string"),value:r.default.string()})))}(c||(t.Script=c={})),function(e){e.SpecialNumberSchema=r.default.lazy((()=>r.default.enum(["NaN","-0","Infinity","-Infinity"])))}(c||(t.Script=c={})),function(e){e.NumberValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("number"),value:r.default.union([r.default.number(),e.SpecialNumberSchema])})))}(c||(t.Script=c={})),function(e){e.BooleanValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("boolean"),value:r.default.boolean()})))}(c||(t.Script=c={})),function(e){e.BigIntValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("bigint"),value:r.default.string()})))}(c||(t.Script=c={})),function(e){e.RealmInfoSchema=r.default.lazy((()=>r.default.union([e.WindowRealmInfoSchema,e.DedicatedWorkerRealmInfoSchema,e.SharedWorkerRealmInfoSchema,e.ServiceWorkerRealmInfoSchema,e.WorkerRealmInfoSchema,e.PaintWorkletRealmInfoSchema,e.AudioWorkletRealmInfoSchema,e.WorkletRealmInfoSchema])))}(c||(t.Script=c={})),function(e){e.BaseRealmInfoSchema=r.default.lazy((()=>r.default.object({realm:e.RealmSchema,origin:r.default.string()})))}(c||(t.Script=c={})),function(e){e.WindowRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("window"),context:o.BrowsingContextSchema,sandbox:r.default.string().optional()}))))}(c||(t.Script=c={})),function(e){e.DedicatedWorkerRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("dedicated-worker"),owners:r.default.tuple([e.RealmSchema])}))))}(c||(t.Script=c={})),function(e){e.SharedWorkerRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("shared-worker"),owners:r.default.array(e.RealmSchema).min(1)}))))}(c||(t.Script=c={})),function(e){e.ServiceWorkerRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("service-worker"),owners:r.default.array(e.RealmSchema)}))))}(c||(t.Script=c={})),function(e){e.WorkerRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("worker")}))))}(c||(t.Script=c={})),function(e){e.PaintWorkletRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("paint-worklet")}))))}(c||(t.Script=c={})),function(e){e.AudioWorkletRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("audio-worklet")}))))}(c||(t.Script=c={})),function(e){e.WorkletRealmInfoSchema=r.default.lazy((()=>e.BaseRealmInfoSchema.and(r.default.object({type:r.default.literal("worklet")}))))}(c||(t.Script=c={})),function(e){e.RealmTypeSchema=r.default.lazy((()=>r.default.enum(["window","dedicated-worker","shared-worker","service-worker","worker","paint-worklet","audio-worklet","worklet"])))}(c||(t.Script=c={})),function(e){e.ListRemoteValueSchema=r.default.lazy((()=>r.default.array(e.RemoteValueSchema)))}(c||(t.Script=c={})),function(e){e.MappingRemoteValueSchema=r.default.lazy((()=>r.default.array(r.default.tuple([r.default.union([e.RemoteValueSchema,r.default.string()]),e.RemoteValueSchema]))))}(c||(t.Script=c={})),function(e){e.RemoteValueSchema=r.default.lazy((()=>r.default.union([e.PrimitiveProtocolValueSchema,e.SymbolRemoteValueSchema,e.ArrayRemoteValueSchema,e.ObjectRemoteValueSchema,e.FunctionRemoteValueSchema,e.RegExpRemoteValueSchema,e.DateRemoteValueSchema,e.MapRemoteValueSchema,e.SetRemoteValueSchema,e.WeakMapRemoteValueSchema,e.WeakSetRemoteValueSchema,e.IteratorRemoteValueSchema,e.GeneratorRemoteValueSchema,e.ErrorRemoteValueSchema,e.ProxyRemoteValueSchema,e.PromiseRemoteValueSchema,e.TypedArrayRemoteValueSchema,e.ArrayBufferRemoteValueSchema,e.NodeListRemoteValueSchema,e.HtmlCollectionRemoteValueSchema,e.NodeRemoteValueSchema,e.WindowProxyRemoteValueSchema])))}(c||(t.Script=c={})),function(e){e.RemoteReferenceSchema=r.default.lazy((()=>r.default.union([e.SharedReferenceSchema,e.RemoteObjectReferenceSchema])))}(c||(t.Script=c={})),function(e){e.SharedReferenceSchema=r.default.lazy((()=>r.default.object({sharedId:e.SharedIdSchema,handle:e.HandleSchema.optional()}).and(t.ExtensibleSchema)))}(c||(t.Script=c={})),function(e){e.RemoteObjectReferenceSchema=r.default.lazy((()=>r.default.object({handle:e.HandleSchema,sharedId:e.SharedIdSchema.optional()}).and(t.ExtensibleSchema)))}(c||(t.Script=c={})),function(e){e.SymbolRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("symbol"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.ArrayRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("array"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.ListRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.ObjectRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("object"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.MappingRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.FunctionRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("function"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.RegExpRemoteValueSchema=r.default.lazy((()=>r.default.object({handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()}).and(e.RegExpLocalValueSchema)))}(c||(t.Script=c={})),function(e){e.DateRemoteValueSchema=r.default.lazy((()=>r.default.object({handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()}).and(e.DateLocalValueSchema)))}(c||(t.Script=c={})),function(e){e.MapRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("map"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.MappingRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.SetRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("set"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.ListRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.WeakMapRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("weakmap"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.WeakSetRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("weakset"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.IteratorRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("iterator"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.GeneratorRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("generator"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.ErrorRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("error"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.ProxyRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("proxy"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.PromiseRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("promise"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.TypedArrayRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("typedarray"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.ArrayBufferRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("arraybuffer"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.NodeListRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("nodelist"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.ListRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.HtmlCollectionRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("htmlcollection"),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.ListRemoteValueSchema.optional()})))}(c||(t.Script=c={})),function(e){e.NodeRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("node"),sharedId:e.SharedIdSchema.optional(),handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional(),value:e.NodePropertiesSchema.optional()})))}(c||(t.Script=c={})),function(e){e.NodePropertiesSchema=r.default.lazy((()=>r.default.object({nodeType:t.JsUintSchema,childNodeCount:t.JsUintSchema,attributes:r.default.record(r.default.string(),r.default.string()).optional(),children:r.default.array(e.NodeRemoteValueSchema).optional(),localName:r.default.string().optional(),mode:r.default.enum(["open","closed"]).optional(),namespaceURI:r.default.string().optional(),nodeValue:r.default.string().optional(),shadowRoot:r.default.union([e.NodeRemoteValueSchema,r.default.null()]).optional()})))}(c||(t.Script=c={})),function(e){e.WindowProxyRemoteValueSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("window"),value:e.WindowProxyPropertiesSchema,handle:e.HandleSchema.optional(),internalId:e.InternalIdSchema.optional()})))}(c||(t.Script=c={})),function(e){e.WindowProxyPropertiesSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema})))}(c||(t.Script=c={})),function(e){e.ResultOwnershipSchema=r.default.lazy((()=>r.default.enum(["root","none"])))}(c||(t.Script=c={})),function(e){e.SerializationOptionsSchema=r.default.lazy((()=>r.default.object({maxDomDepth:r.default.union([t.JsUintSchema,r.default.null()]).default(0).optional(),maxObjectDepth:r.default.union([t.JsUintSchema,r.default.null()]).default(null).optional(),includeShadowTree:r.default.enum(["none","open","all"]).default("none").optional()})))}(c||(t.Script=c={})),function(e){e.SharedIdSchema=r.default.lazy((()=>r.default.string()))}(c||(t.Script=c={})),function(e){e.StackFrameSchema=r.default.lazy((()=>r.default.object({columnNumber:t.JsUintSchema,functionName:r.default.string(),lineNumber:t.JsUintSchema,url:r.default.string()})))}(c||(t.Script=c={})),function(e){e.StackTraceSchema=r.default.lazy((()=>r.default.object({callFrames:r.default.array(e.StackFrameSchema)})))}(c||(t.Script=c={})),function(e){e.SourceSchema=r.default.lazy((()=>r.default.object({realm:e.RealmSchema,context:o.BrowsingContextSchema.optional()})))}(c||(t.Script=c={})),function(e){e.RealmTargetSchema=r.default.lazy((()=>r.default.object({realm:e.RealmSchema})))}(c||(t.Script=c={})),function(e){e.ContextTargetSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema,sandbox:r.default.string().optional()})))}(c||(t.Script=c={})),function(e){e.TargetSchema=r.default.lazy((()=>r.default.union([e.RealmTargetSchema,e.ContextTargetSchema])))}(c||(t.Script=c={})),function(e){e.AddPreloadScriptSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.addPreloadScript"),params:e.AddPreloadScriptParametersSchema})))}(c||(t.Script=c={})),function(e){e.AddPreloadScriptParametersSchema=r.default.lazy((()=>r.default.object({functionDeclaration:r.default.string(),arguments:r.default.array(e.ChannelValueSchema).optional(),contexts:r.default.array(o.BrowsingContextSchema).min(1).optional(),sandbox:r.default.string().optional()})))}(c||(t.Script=c={})),function(e){e.AddPreloadScriptResultSchema=r.default.lazy((()=>r.default.object({script:e.PreloadScriptSchema})))}(c||(t.Script=c={})),function(e){e.DisownSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.disown"),params:e.DisownParametersSchema})))}(c||(t.Script=c={})),function(e){e.DisownParametersSchema=r.default.lazy((()=>r.default.object({handles:r.default.array(e.HandleSchema),target:e.TargetSchema})))}(c||(t.Script=c={})),function(e){e.CallFunctionParametersSchema=r.default.lazy((()=>r.default.object({functionDeclaration:r.default.string(),awaitPromise:r.default.boolean(),target:e.TargetSchema,arguments:r.default.array(e.LocalValueSchema).optional(),resultOwnership:e.ResultOwnershipSchema.optional(),serializationOptions:e.SerializationOptionsSchema.optional(),this:e.LocalValueSchema.optional(),userActivation:r.default.boolean().default(!1).optional()})))}(c||(t.Script=c={})),function(e){e.CallFunctionSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.callFunction"),params:e.CallFunctionParametersSchema})))}(c||(t.Script=c={})),function(e){e.EvaluateSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.evaluate"),params:e.EvaluateParametersSchema})))}(c||(t.Script=c={})),function(e){e.EvaluateParametersSchema=r.default.lazy((()=>r.default.object({expression:r.default.string(),target:e.TargetSchema,awaitPromise:r.default.boolean(),resultOwnership:e.ResultOwnershipSchema.optional(),serializationOptions:e.SerializationOptionsSchema.optional(),userActivation:r.default.boolean().default(!1).optional()})))}(c||(t.Script=c={})),function(e){e.GetRealmsSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.getRealms"),params:e.GetRealmsParametersSchema})))}(c||(t.Script=c={})),function(e){e.GetRealmsParametersSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema.optional(),type:e.RealmTypeSchema.optional()})))}(c||(t.Script=c={})),function(e){e.GetRealmsResultSchema=r.default.lazy((()=>r.default.object({realms:r.default.array(e.RealmInfoSchema)})))}(c||(t.Script=c={})),function(e){e.RemovePreloadScriptSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.removePreloadScript"),params:e.RemovePreloadScriptParametersSchema})))}(c||(t.Script=c={})),function(e){e.RemovePreloadScriptParametersSchema=r.default.lazy((()=>r.default.object({script:e.PreloadScriptSchema})))}(c||(t.Script=c={})),function(e){e.MessageParametersSchema=r.default.lazy((()=>r.default.object({channel:e.ChannelSchema,data:e.RemoteValueSchema,source:e.SourceSchema})))}(c||(t.Script=c={})),function(e){e.RealmCreatedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.realmCreated"),params:e.RealmInfoSchema})))}(c||(t.Script=c={})),function(e){e.MessageSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.message"),params:e.MessageParametersSchema})))}(c||(t.Script=c={})),function(e){e.RealmDestroyedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("script.realmDestroyed"),params:e.RealmDestroyedParametersSchema})))}(c||(t.Script=c={})),function(e){e.RealmDestroyedParametersSchema=r.default.lazy((()=>r.default.object({realm:e.RealmSchema})))}(c||(t.Script=c={})),t.StorageCommandSchema=r.default.lazy((()=>r.default.union([d.DeleteCookiesSchema,d.GetCookiesSchema,d.SetCookieSchema]))),t.StorageResultSchema=r.default.lazy((()=>r.default.union([d.DeleteCookiesResultSchema,d.GetCookiesResultSchema,d.SetCookieResultSchema]))),function(e){e.PartitionKeySchema=r.default.lazy((()=>r.default.object({userContext:r.default.string().optional(),sourceOrigin:r.default.string().optional()}).and(t.ExtensibleSchema)))}(d||(t.Storage=d={})),function(e){e.GetCookiesSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("storage.getCookies"),params:e.GetCookiesParametersSchema})))}(d||(t.Storage=d={})),function(e){e.CookieFilterSchema=r.default.lazy((()=>r.default.object({name:r.default.string().optional(),value:i.BytesValueSchema.optional(),domain:r.default.string().optional(),path:r.default.string().optional(),size:t.JsUintSchema.optional(),httpOnly:r.default.boolean().optional(),secure:r.default.boolean().optional(),sameSite:i.SameSiteSchema.optional(),expiry:t.JsUintSchema.optional()}).and(t.ExtensibleSchema)))}(d||(t.Storage=d={})),function(e){e.BrowsingContextPartitionDescriptorSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("context"),context:o.BrowsingContextSchema})))}(d||(t.Storage=d={})),function(e){e.StorageKeyPartitionDescriptorSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("storageKey"),userContext:r.default.string().optional(),sourceOrigin:r.default.string().optional()}).and(t.ExtensibleSchema)))}(d||(t.Storage=d={})),function(e){e.PartitionDescriptorSchema=r.default.lazy((()=>r.default.union([e.BrowsingContextPartitionDescriptorSchema,e.StorageKeyPartitionDescriptorSchema])))}(d||(t.Storage=d={})),function(e){e.GetCookiesParametersSchema=r.default.lazy((()=>r.default.object({filter:e.CookieFilterSchema.optional(),partition:e.PartitionDescriptorSchema.optional()})))}(d||(t.Storage=d={})),function(e){e.GetCookiesResultSchema=r.default.lazy((()=>r.default.object({cookies:r.default.array(i.CookieSchema),partitionKey:e.PartitionKeySchema})))}(d||(t.Storage=d={})),function(e){e.SetCookieSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("storage.setCookie"),params:e.SetCookieParametersSchema})))}(d||(t.Storage=d={})),function(e){e.PartialCookieSchema=r.default.lazy((()=>r.default.object({name:r.default.string(),value:i.BytesValueSchema,domain:r.default.string(),path:r.default.string().optional(),httpOnly:r.default.boolean().optional(),secure:r.default.boolean().optional(),sameSite:i.SameSiteSchema.optional(),expiry:t.JsUintSchema.optional()}).and(t.ExtensibleSchema)))}(d||(t.Storage=d={})),function(e){e.SetCookieParametersSchema=r.default.lazy((()=>r.default.object({cookie:e.PartialCookieSchema,partition:e.PartitionDescriptorSchema.optional()})))}(d||(t.Storage=d={})),function(e){e.SetCookieResultSchema=r.default.lazy((()=>r.default.object({partitionKey:e.PartitionKeySchema})))}(d||(t.Storage=d={})),function(e){e.DeleteCookiesSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("storage.deleteCookies"),params:e.DeleteCookiesParametersSchema})))}(d||(t.Storage=d={})),function(e){e.DeleteCookiesParametersSchema=r.default.lazy((()=>r.default.object({filter:e.CookieFilterSchema.optional(),partition:e.PartitionDescriptorSchema.optional()})))}(d||(t.Storage=d={})),function(e){e.DeleteCookiesResultSchema=r.default.lazy((()=>r.default.object({partitionKey:e.PartitionKeySchema})))}(d||(t.Storage=d={})),t.LogEventSchema=r.default.lazy((()=>u.EntryAddedSchema)),function(e){e.LevelSchema=r.default.lazy((()=>r.default.enum(["debug","info","warn","error"])))}(u||(t.Log=u={})),function(e){e.EntrySchema=r.default.lazy((()=>r.default.union([e.GenericLogEntrySchema,e.ConsoleLogEntrySchema,e.JavascriptLogEntrySchema])))}(u||(t.Log=u={})),function(e){e.BaseLogEntrySchema=r.default.lazy((()=>r.default.object({level:e.LevelSchema,source:c.SourceSchema,text:r.default.union([r.default.string(),r.default.null()]),timestamp:t.JsUintSchema,stackTrace:c.StackTraceSchema.optional()})))}(u||(t.Log=u={})),function(e){e.GenericLogEntrySchema=r.default.lazy((()=>e.BaseLogEntrySchema.and(r.default.object({type:r.default.string()}))))}(u||(t.Log=u={})),function(e){e.ConsoleLogEntrySchema=r.default.lazy((()=>e.BaseLogEntrySchema.and(r.default.object({type:r.default.literal("console"),method:r.default.string(),args:r.default.array(c.RemoteValueSchema)}))))}(u||(t.Log=u={})),function(e){e.JavascriptLogEntrySchema=r.default.lazy((()=>e.BaseLogEntrySchema.and(r.default.object({type:r.default.literal("javascript")}))))}(u||(t.Log=u={})),function(e){e.EntryAddedSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("log.entryAdded"),params:e.EntrySchema})))}(u||(t.Log=u={})),t.InputCommandSchema=r.default.lazy((()=>r.default.union([l.PerformActionsSchema,l.ReleaseActionsSchema,l.SetFilesSchema]))),function(e){e.ElementOriginSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("element"),element:c.SharedReferenceSchema})))}(l||(t.Input=l={})),function(e){e.PerformActionsParametersSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema,actions:r.default.array(e.SourceActionsSchema)})))}(l||(t.Input=l={})),function(e){e.NoneSourceActionsSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("none"),id:r.default.string(),actions:r.default.array(e.NoneSourceActionSchema)})))}(l||(t.Input=l={})),function(e){e.KeySourceActionsSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("key"),id:r.default.string(),actions:r.default.array(e.KeySourceActionSchema)})))}(l||(t.Input=l={})),function(e){e.PointerSourceActionsSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pointer"),id:r.default.string(),parameters:e.PointerParametersSchema.optional(),actions:r.default.array(e.PointerSourceActionSchema)})))}(l||(t.Input=l={})),function(e){e.PerformActionsSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("input.performActions"),params:e.PerformActionsParametersSchema})))}(l||(t.Input=l={})),function(e){e.SourceActionsSchema=r.default.lazy((()=>r.default.union([e.NoneSourceActionsSchema,e.KeySourceActionsSchema,e.PointerSourceActionsSchema,e.WheelSourceActionsSchema])))}(l||(t.Input=l={})),function(e){e.NoneSourceActionSchema=r.default.lazy((()=>e.PauseActionSchema))}(l||(t.Input=l={})),function(e){e.KeySourceActionSchema=r.default.lazy((()=>r.default.union([e.PauseActionSchema,e.KeyDownActionSchema,e.KeyUpActionSchema])))}(l||(t.Input=l={})),function(e){e.PointerTypeSchema=r.default.lazy((()=>r.default.enum(["mouse","pen","touch"])))}(l||(t.Input=l={})),function(e){e.PointerParametersSchema=r.default.lazy((()=>r.default.object({pointerType:e.PointerTypeSchema.default("mouse").optional()})))}(l||(t.Input=l={})),function(e){e.WheelSourceActionsSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("wheel"),id:r.default.string(),actions:r.default.array(e.WheelSourceActionSchema)})))}(l||(t.Input=l={})),function(e){e.PointerSourceActionSchema=r.default.lazy((()=>r.default.union([e.PauseActionSchema,e.PointerDownActionSchema,e.PointerUpActionSchema,e.PointerMoveActionSchema])))}(l||(t.Input=l={})),function(e){e.WheelSourceActionSchema=r.default.lazy((()=>r.default.union([e.PauseActionSchema,e.WheelScrollActionSchema])))}(l||(t.Input=l={})),function(e){e.PauseActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pause"),duration:t.JsUintSchema.optional()})))}(l||(t.Input=l={})),function(e){e.KeyDownActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("keyDown"),value:r.default.string()})))}(l||(t.Input=l={})),function(e){e.KeyUpActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("keyUp"),value:r.default.string()})))}(l||(t.Input=l={})),function(e){e.PointerUpActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pointerUp"),button:t.JsUintSchema}).and(e.PointerCommonPropertiesSchema)))}(l||(t.Input=l={})),function(e){e.PointerDownActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pointerDown"),button:t.JsUintSchema}).and(e.PointerCommonPropertiesSchema)))}(l||(t.Input=l={})),function(e){e.PointerMoveActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("pointerMove"),x:t.JsIntSchema,y:t.JsIntSchema,duration:t.JsUintSchema.optional(),origin:e.OriginSchema.optional()}).and(e.PointerCommonPropertiesSchema)))}(l||(t.Input=l={})),function(e){e.WheelScrollActionSchema=r.default.lazy((()=>r.default.object({type:r.default.literal("scroll"),x:t.JsIntSchema,y:t.JsIntSchema,deltaX:t.JsIntSchema,deltaY:t.JsIntSchema,duration:t.JsUintSchema.optional(),origin:e.OriginSchema.default("viewport").optional()})))}(l||(t.Input=l={})),function(e){e.PointerCommonPropertiesSchema=r.default.lazy((()=>r.default.object({width:t.JsUintSchema.default(1).optional(),height:t.JsUintSchema.default(1).optional(),pressure:r.default.number().default(0).optional(),tangentialPressure:r.default.number().default(0).optional(),twist:r.default.number().int().nonnegative().gte(0).lte(359).default(0).optional(),altitudeAngle:r.default.number().gte(0).lte(1.5707963267948966).default(0).optional(),azimuthAngle:r.default.number().gte(0).lte(6.283185307179586).default(0).optional()})))}(l||(t.Input=l={})),function(e){e.OriginSchema=r.default.lazy((()=>r.default.union([r.default.literal("viewport"),r.default.literal("pointer"),e.ElementOriginSchema])))}(l||(t.Input=l={})),function(e){e.ReleaseActionsSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("input.releaseActions"),params:e.ReleaseActionsParametersSchema})))}(l||(t.Input=l={})),function(e){e.ReleaseActionsParametersSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema})))}(l||(t.Input=l={})),function(e){e.SetFilesSchema=r.default.lazy((()=>r.default.object({method:r.default.literal("input.setFiles"),params:e.SetFilesParametersSchema})))}(l||(t.Input=l={})),function(e){e.SetFilesParametersSchema=r.default.lazy((()=>r.default.object({context:o.BrowsingContextSchema,element:c.SharedReferenceSchema,files:r.default.array(r.default.string())})))}(l||(t.Input=l={}))}(On);var Mn=e&&e.__createBinding||(Object.create?function(e,t,a,r){void 0===r&&(r=a);var n=Object.getOwnPropertyDescriptor(t,a);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,r,n)}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]}),An=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),Bn=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)"default"!==a&&Object.prototype.hasOwnProperty.call(e,a)&&Mn(t,e,a);return An(t,e),t};Object.defineProperty(un,"__esModule",{value:!0}),un.Permissions=un.Cdp=un.Storage=un.Input=un.Session=un.BrowsingContext=un.Script=un.Network=un.parseObject=void 0;const zn=ln,Dn=g,Ln=Bn(_n),Zn=Bn(On);function Un(e,t){const a=t.safeParse(e);if(a.success)return a.data;const r=a.error.errors.map((e=>`${e.message} in ${e.path.map((e=>JSON.stringify(e))).join("/")}.`)).join(" ");throw new Dn.InvalidArgumentException(r)}var Fn,qn,Vn,$n,Kn,Hn,Wn,Jn;un.parseObject=Un,function(e){e.parseAddInterceptParameters=function(e){return Un(e,Zn.Network.AddInterceptParametersSchema)},e.parseContinueRequestParameters=function(e){return Un(e,Zn.Network.ContinueRequestParametersSchema)},e.parseContinueResponseParameters=function(e){return Un(e,Zn.Network.ContinueResponseParametersSchema)},e.parseContinueWithAuthParameters=function(e){return Un(e,Zn.Network.ContinueWithAuthParametersSchema)},e.parseFailRequestParameters=function(e){return Un(e,Zn.Network.FailRequestParametersSchema)},e.parseProvideResponseParameters=function(e){return Un(e,Zn.Network.ProvideResponseParametersSchema)},e.parseRemoveInterceptParameters=function(e){return Un(e,Zn.Network.RemoveInterceptParametersSchema)}}(Fn||(un.Network=Fn={})),function(e){e.parseGetRealmsParams=function(e){return Un(e,Zn.Script.GetRealmsParametersSchema)},e.parseEvaluateParams=function(e){return Un(e,Zn.Script.EvaluateParametersSchema)},e.parseDisownParams=function(e){return Un(e,Zn.Script.DisownParametersSchema)},e.parseAddPreloadScriptParams=function(e){return Un(e,Zn.Script.AddPreloadScriptParametersSchema)},e.parseRemovePreloadScriptParams=function(e){return Un(e,Zn.Script.RemovePreloadScriptParametersSchema)},e.parseCallFunctionParams=function(e){return Un(e,Zn.Script.CallFunctionParametersSchema)}}(qn||(un.Script=qn={})),function(e){e.parseActivateParams=function(e){return Un(e,Zn.BrowsingContext.ActivateParametersSchema)},e.parseGetTreeParams=function(e){return Un(e,Zn.BrowsingContext.GetTreeParametersSchema)},e.parseNavigateParams=function(e){return Un(e,Zn.BrowsingContext.NavigateParametersSchema)},e.parseReloadParams=function(e){return Un(e,Zn.BrowsingContext.ReloadParametersSchema)},e.parseCreateParams=function(e){return Un(e,Zn.BrowsingContext.CreateParametersSchema)},e.parseCloseParams=function(e){return Un(e,Zn.BrowsingContext.CloseParametersSchema)},e.parseCaptureScreenshotParams=function(e){return Un(e,Zn.BrowsingContext.CaptureScreenshotParametersSchema)},e.parsePrintParams=function(e){return Un(e,Zn.BrowsingContext.PrintParametersSchema)},e.parseSetViewportParams=function(e){return Un(e,Zn.BrowsingContext.SetViewportParametersSchema)},e.parseTraverseHistoryParams=function(e){return Un(e,Zn.BrowsingContext.TraverseHistoryParametersSchema)},e.parseHandleUserPromptParameters=function(e){return Un(e,Zn.BrowsingContext.HandleUserPromptParametersSchema)}}(Vn||(un.BrowsingContext=Vn={})),function(e){e.parseSubscribeParams=function(e){return Un(e,Zn.Session.SubscriptionRequestSchema)}}($n||(un.Session=$n={})),function(e){e.parsePerformActionsParams=function(e){return Un(e,Zn.Input.PerformActionsParametersSchema)},e.parseReleaseActionsParams=function(e){return Un(e,Zn.Input.ReleaseActionsParametersSchema)},e.parseSetFilesParams=function(e){return Un(e,Zn.Input.SetFilesParametersSchema)}}(Kn||(un.Input=Kn={})),function(e){e.parseGetCookiesParams=function(e){return Un(e,Zn.Storage.GetCookiesParametersSchema)},e.parseSetCookieParams=function(e){return Un(e,Zn.Storage.SetCookieParametersSchema)},e.parseDeleteCookiesParams=function(e){return Un(e,Zn.Storage.DeleteCookiesParametersSchema)}}(Hn||(un.Storage=Hn={})),function(e){const t=zn.z.object({method:zn.z.string(),params:zn.z.object({}).passthrough().optional(),session:zn.z.string().optional()}),a=zn.z.object({context:Zn.BrowsingContext.BrowsingContextSchema});e.parseSendCommandRequest=function(e){return Un(e,t)},e.parseGetSessionRequest=function(e){return Un(e,a)}}(Wn||(un.Cdp=Wn={})),function(e){e.parseSetPermissionsParams=function(e){return Un(e,Ln.Permissions.SetPermissionParametersSchema)}}(Jn||(un.Permissions=Jn={}));var Gn=e&&e.__createBinding||(Object.create?function(e,t,a,r){void 0===r&&(r=a);var n=Object.getOwnPropertyDescriptor(t,a);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[a]}}),Object.defineProperty(e,r,n)}:function(e,t,a,r){void 0===r&&(r=a),e[r]=t[a]}),Xn=e&&e.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),Yn=e&&e.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var a in e)"default"!==a&&Object.prototype.hasOwnProperty.call(e,a)&&Gn(t,e,a);return Xn(t,e),t};Object.defineProperty(dn,"__esModule",{value:!0}),dn.BidiParser=void 0;const Qn=Yn(un);dn.BidiParser=class{parseActivateParams(e){return Qn.BrowsingContext.parseActivateParams(e)}parseCaptureScreenshotParams(e){return Qn.BrowsingContext.parseCaptureScreenshotParams(e)}parseCloseParams(e){return Qn.BrowsingContext.parseCloseParams(e)}parseCreateParams(e){return Qn.BrowsingContext.parseCreateParams(e)}parseGetTreeParams(e){return Qn.BrowsingContext.parseGetTreeParams(e)}parseHandleUserPromptParams(e){return Qn.BrowsingContext.parseHandleUserPromptParameters(e)}parseNavigateParams(e){return Qn.BrowsingContext.parseNavigateParams(e)}parsePrintParams(e){return Qn.BrowsingContext.parsePrintParams(e)}parseReloadParams(e){return Qn.BrowsingContext.parseReloadParams(e)}parseSetViewportParams(e){return Qn.BrowsingContext.parseSetViewportParams(e)}parseTraverseHistoryParams(e){return Qn.BrowsingContext.parseTraverseHistoryParams(e)}parseGetSessionParams(e){return Qn.Cdp.parseGetSessionRequest(e)}parseSendCommandParams(e){return Qn.Cdp.parseSendCommandRequest(e)}parsePerformActionsParams(e){return Qn.Input.parsePerformActionsParams(e)}parseReleaseActionsParams(e){return Qn.Input.parseReleaseActionsParams(e)}parseSetFilesParams(e){return Qn.Input.parseSetFilesParams(e)}parseAddInterceptParams(e){return Qn.Network.parseAddInterceptParameters(e)}parseContinueRequestParams(e){return Qn.Network.parseContinueRequestParameters(e)}parseContinueResponseParams(e){return Qn.Network.parseContinueResponseParameters(e)}parseContinueWithAuthParams(e){return Qn.Network.parseContinueWithAuthParameters(e)}parseFailRequestParams(e){return Qn.Network.parseFailRequestParameters(e)}parseProvideResponseParams(e){return Qn.Network.parseProvideResponseParameters(e)}parseRemoveInterceptParams(e){return Qn.Network.parseRemoveInterceptParameters(e)}parseSetPermissionsParams(e){return Qn.Permissions.parseSetPermissionsParams(e)}parseAddPreloadScriptParams(e){return Qn.Script.parseAddPreloadScriptParams(e)}parseCallFunctionParams(e){return Qn.Script.parseCallFunctionParams(e)}parseDisownParams(e){return Qn.Script.parseDisownParams(e)}parseEvaluateParams(e){return Qn.Script.parseEvaluateParams(e)}parseGetRealmsParams(e){return Qn.Script.parseGetRealmsParams(e)}parseRemovePreloadScriptParams(e){return Qn.Script.parseRemovePreloadScriptParams(e)}parseSubscribeParams(e){return Qn.Session.parseSubscribeParams(e)}parseDeleteCookiesParams(e){return Qn.Storage.parseDeleteCookiesParams(e)}parseGetCookiesParams(e){return Qn.Storage.parseGetCookiesParams(e)}parseSetCookieParams(e){return Qn.Storage.parseSetCookieParams(e)}};var es={};Object.defineProperty(es,"__esModule",{value:!0}),es.log=es.generatePage=void 0;const ts=l;function as(e){const t=e.split(":")[0],a=`${t}_log`,r=document.getElementById(a);if(r)return r;const n=document.getElementById("details"),s=document.createElement("div");s.className="divider",n.appendChild(s);const o=document.createElement("div");return o.className="item",o.innerHTML=`<h3>${t}</h3><div id="${a}" class="log"></div>`,n.appendChild(o),document.getElementById(a)}function rs(e){return"object"==typeof e?JSON.stringify(e,null,2):e}es.generatePage=function(){globalThis.document.documentElement&&(globalThis.document.documentElement.innerHTML='<!DOCTYPE html><title>BiDi-CDP Mapper</title><style>body{font-family: Roboto, serif; font-size: 13px; color: #202124;}.log{padding: 12px; font-family: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace; font-size: 11px; line-height: 180%; background: #f1f3f4; border-radius: 4px;}.pre{overflow-wrap: break-word; padding: 10px;}.card{margin: 60px auto; padding: 2px 0; max-width: 900px; box-shadow: 0 1px 4px rgba(0, 0, 0, 0.15), 0 1px 6px rgba(0, 0, 0, 0.2); border-radius: 8px;}.divider{height: 1px; background: #f0f0f0;}.item{padding: 16px 20px;}</style><div class="card"><div class="item"><h1>BiDi-CDP Mapper is controlling this tab</h1><p>Closing or reloading it will stop the BiDi process. <a target="_blank" title="BiDi-CDP Mapper GitHub Repository" href="https://github.com/GoogleChromeLabs/chromium-bidi">Details.</a></p></div><div class="divider"></div><details id="details"><summary class="item">Debug information</summary></details></div>',as(ts.LogType.debugInfo),as(ts.LogType.bidi),as(ts.LogType.cdp))},es.log=function(t,...a){if(!globalThis.document.documentElement)return;t.startsWith(ts.LogType.bidi)||e.window?.sendDebugMessage?.(JSON.stringify({logType:t,messages:a}));const r=as(t),n=document.createElement("div");n.className="pre",n.textContent=[t,...a].map(rs).join(" "),r.appendChild(n)};var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.WindowCdpTransport=ns.WindowBidiTransport=void 0;const ss=l,os=es;class is{static LOGGER_PREFIX_RECV=`${ss.LogType.bidi}:RECV \u25c2`;static LOGGER_PREFIX_SEND=`${ss.LogType.bidi}:SEND \u25b8`;#sr=null;constructor(){window.onBidiMessage=e=>{(0,os.log)(is.LOGGER_PREFIX_RECV,e);try{const t=is.#ir(e);this.#sr?.call(null,t)}catch(t){const a=t instanceof Error?t:new Error(t);this.#cr(e,"invalid argument",a,null)}}}setOnMessage(e){this.#sr=e}sendMessage(e){(0,os.log)(is.LOGGER_PREFIX_SEND,e);const t=JSON.stringify(e);window.sendBidiResponse(t)}close(){this.#sr=null,window.onBidiMessage=null}#cr(e,t,a,r){const n=is.#dr(e,t,a);r?this.sendMessage({...n,channel:r}):this.sendMessage(n)}static#ur(e){return null===e?"null":Array.isArray(e)?"array":typeof e}static#dr(e,t,a){let r;try{const t=JSON.parse(e);"object"===is.#ur(t)&&"id"in t&&(r=t.id)}catch{}return{type:"error",id:r,error:t,message:a.message}}static#ir(e){let t;try{t=JSON.parse(e)}catch{throw new Error("Cannot parse data as JSON")}const a=is.#ur(t);if("object"!==a)throw new Error(`Expected JSON object but got ${a}`);const{id:r,method:n,params:s}=t,o=is.#ur(r);if("number"!==o||!Number.isInteger(r)||r<0)throw new Error(`Expected unsigned integer but got ${o}`);const i=is.#ur(n);if("string"!==i)throw new Error(`Expected string method but got ${i}`);const c=is.#ur(s);if("object"!==c)throw new Error(`Expected object params but got ${c}`);let d=t.channel;if(void 0!==d){const e=is.#ur(d);if("string"!==e)throw new Error(`Expected string channel but got ${e}`);""===d&&(d=void 0)}return{id:r,method:n,params:s,channel:d}}}ns.WindowBidiTransport=is;ns.WindowCdpTransport=class{#sr=null;constructor(){window.cdp.onmessage=e=>{this.#sr?.call(null,e)}}setOnMessage(e){this.#sr=e}sendMessage(e){window.cdp.send(e)}close(){this.#sr=null,window.cdp.onmessage=null}},
/**
	 * Copyright 2021 Google LLC.
	 * Copyright (c) Microsoft Corporation.
	 *
	 * Licensed under the Apache License, Version 2.0 (the "License");
	 * you may not use this file except in compliance with the License.
	 * You may obtain a copy of the License at
	 *
	 *     http://www.apache.org/licenses/LICENSE-2.0
	 *
	 * Unless required by applicable law or agreed to in writing, software
	 * distributed under the License is distributed on an "AS IS" BASIS,
	 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
	 * See the License for the specific language governing permissions and
	 * limitations under the License.
	 *
	 * @license
	 */
Object.defineProperty(a,"__esModule",{value:!0});const cs=r,ds=en,us=l,ls=dn,hs=es,ps=ns;(0,hs.generatePage)();const ms=new ps.WindowBidiTransport,fs=new ps.WindowCdpTransport,gs=new ds.MapperCdpConnection(fs,hs.log);return window.runMapperInstance=async(e,t)=>{await async function(e,t){console.log("Launching Mapper instance with selfTargetId:",e);const a=await cs.BidiServer.createAndStart(ms,gs,await gs.createBrowserSession(),e,t,new ls.BidiParser,hs.log);return(0,hs.log)(us.LogType.debugInfo,"Mapper instance has been launched"),a}(e,t)},a}();
//# sourceMappingURL=mapperTab.js.map
