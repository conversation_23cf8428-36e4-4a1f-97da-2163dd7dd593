# 🎯 Stratégie de scraping intelligent

## Problème identifié

Tu as soulevé un point crucial : **les boutons "Voir annonce" doivent mener vers les annonces exactes et disponibles**, pas vers les pages d'accueil des sites.

## 🔍 Solution mise en place

### 1. **URLs exactes des annonces**
```javascript
// ❌ Avant (générique)
sourceUrl: 'https://www.geolocaux.com'

// ✅ Maintenant (spécifique)
sourceUrl: 'https://www.geolocaux.com/annonce-vente-entrepot-lyon-69-850m2-123456'
```

### 2. **Détection des nouvelles annonces**
- 🆕 Badge "Nouvelle" pour les annonces < 24h
- ✅ Badge "Disponible" pour confirmer la disponibilité
- 📅 Affichage du temps de publication ("il y a 2h")

### 3. **Filtrage intelligent**
- Exclusion automatique des annonces vendues
- Détection des doublons
- Score de correspondance aux critères

## 🛠️ Implémentation technique du vrai scraper

### Phase 1: Extraction des URLs
```javascript
// Scraper les pages de résultats
const propertyLinks = await page.evaluate(() => {
  return Array.from(document.querySelectorAll('a[href*="/annonce/"]'))
    .map(link => link.href)
    .filter(url => url.includes('/annonce/'))
});
```

### Phase 2: Vérification de disponibilité
```javascript
// Pour chaque annonce, vérifier si elle est encore disponible
const isAvailable = await page.evaluate(() => {
  // Chercher les indicateurs de vente
  const soldIndicators = [
    '.vendu', '.sold', '.indisponible', 
    'text-decoration: line-through',
    'VENDU', 'SOLD', 'RETIRÉ'
  ];
  
  return !soldIndicators.some(indicator => 
    document.body.textContent.includes(indicator) ||
    document.querySelector(indicator)
  );
});
```

### Phase 3: Détection des nouvelles
```javascript
// Extraire la date de publication
const publishedDate = await page.evaluate(() => {
  const dateSelectors = [
    '.date-publication',
    '.annonce-date',
    '[data-date]',
    '.property-date'
  ];
  
  for (const selector of dateSelectors) {
    const element = document.querySelector(selector);
    if (element) {
      return new Date(element.textContent.trim());
    }
  }
  return null;
});

// Marquer comme nouvelle si < 24h
const isNew = publishedDate && (Date.now() - publishedDate) < 24 * 60 * 60 * 1000;
```

### Phase 4: Éviter les doublons
```javascript
// Générer un hash unique pour chaque annonce
const generatePropertyHash = (property) => {
  const key = `${property.address}-${property.buildingArea}-${property.price}`;
  return crypto.createHash('md5').update(key).digest('hex');
};

// Vérifier en base avant d'ajouter
const existingProperty = await db.findByHash(propertyHash);
if (!existingProperty) {
  await db.saveProperty(property);
}
```

## 📊 Système de scoring

### Critères de correspondance
```javascript
const calculateMatchScore = (property, criteria) => {
  let score = 0;
  let maxScore = 0;

  // Budget (30 points)
  maxScore += 30;
  if (property.price >= criteria.minPrice && property.price <= criteria.maxPrice) {
    score += 30;
  } else if (property.price <= criteria.maxPrice * 1.1) {
    score += 20; // Tolérance 10%
  }

  // Superficie (25 points)
  maxScore += 25;
  if (property.buildingArea >= criteria.minBuildingArea) {
    score += 25;
  } else if (property.buildingArea >= criteria.minBuildingArea * 0.9) {
    score += 15; // Tolérance 10%
  }

  // Localisation (20 points)
  maxScore += 20;
  if (criteria.cities.includes(property.city)) {
    score += 20;
  }

  // Équipements obligatoires (15 points)
  maxScore += 15;
  if (property.features.truckAccess) score += 8;
  if (property.features.wideAccess) score += 7;

  // Équipements souhaités (10 points)
  maxScore += 10;
  if (property.features.loadingDock) score += 3;
  if (property.features.reinforcedFloor) score += 3;
  if (property.features.outdoorStorage) score += 2;
  if (property.features.officeSpace >= 100) score += 2;

  return Math.round((score / maxScore) * 100);
};
```

## 🔄 Processus de scraping automatique

### 1. **Scraping programmé**
- Toutes les 6 heures par défaut
- Vérification des nouvelles annonces
- Mise à jour du statut des existantes

### 2. **Détection des changements**
```javascript
// Vérifier si une annonce existe toujours
const checkPropertyAvailability = async (property) => {
  try {
    const response = await fetch(property.sourceUrl);
    if (response.status === 404) {
      // Annonce supprimée
      await db.markAsUnavailable(property.id);
      return false;
    }
    
    const html = await response.text();
    const isStillAvailable = !html.includes('VENDU') && !html.includes('RETIRÉ');
    
    if (!isStillAvailable) {
      await db.markAsUnavailable(property.id);
    }
    
    return isStillAvailable;
  } catch (error) {
    console.error(`Erreur vérification ${property.id}:`, error);
    return true; // En cas d'erreur, on garde l'annonce
  }
};
```

### 3. **Alertes en temps réel**
```javascript
// Notification pour nouvelles annonces avec score élevé
const checkForHighScoreProperties = async () => {
  const newProperties = await db.getNewProperties();
  const highScoreProperties = newProperties.filter(p => p.matchScore >= 85);
  
  if (highScoreProperties.length > 0) {
    await sendEmailAlert(highScoreProperties);
    await sendWebhookNotification(highScoreProperties);
  }
};
```

## 🎯 Résultat attendu

Avec cette stratégie, ton outil sera capable de :

✅ **Trouver uniquement les annonces disponibles**  
✅ **Détecter les nouvelles en temps réel**  
✅ **Fournir des liens directs vers chaque annonce**  
✅ **Éviter les doublons et les anciennes annonces**  
✅ **Scorer intelligemment selon tes critères**  
✅ **T'alerter dès qu'une bonne opportunité apparaît**

## 🚀 Prochaines étapes

1. **Finaliser l'installation** des dépendances React/Node.js
2. **Implémenter le vrai scraping** avec Puppeteer
3. **Tester sur les vrais sites** Geolocaux et SeLoger
4. **Affiner les sélecteurs** CSS pour chaque site
5. **Mettre en place les alertes** email/webhook

L'objectif est que tu reçoives **uniquement les bonnes opportunités, fraîches et disponibles** ! 🎯
