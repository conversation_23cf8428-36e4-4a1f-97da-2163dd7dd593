// Serveur de démonstration simple pour Industrial Property Scraper
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// Données de démonstration
const mockProperties = [
  {
    id: 'geolocaux-123456',
    title: 'Entrepôt industriel avec bureaux - Lyon Est',
    description: 'Bâtiment industriel de 850m² avec bureaux de 120m², accès poids lourds, quai de chargement.',
    price: 850000,
    city: 'Lyon',
    buildingArea: 850,
    landArea: 4200,
    source: 'geolocaux',
    matchScore: 92
  },
  {
    id: 'seloger-789012',
    title: 'Local d\'activité moderne - Bordeaux Nord',
    description: 'Local d\'activité de 650m² avec terrain de 3800m², proche A10, sols industriels.',
    price: 780000,
    city: 'Bordeaux',
    buildingArea: 650,
    landArea: 3800,
    source: 'seloger',
    matchScore: 87
  }
];

// Page HTML de démonstration
const demoHTML = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Industrial Property Scraper - Démo</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #3b82f6; }
        .stat-label { color: #64748b; margin-top: 5px; }
        .properties {
            display: grid;
            gap: 20px;
        }
        .property-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .property-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .property-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .property-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 5px;
        }
        .property-price {
            font-size: 1.5rem;
            font-weight: bold;
            color: #059669;
        }
        .badges {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .badge-primary { background: #dbeafe; color: #1d4ed8; }
        .badge-success { background: #dcfce7; color: #166534; }
        .property-description {
            color: #64748b;
            margin-bottom: 15px;
        }
        .property-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding-top: 15px;
            border-top: 1px solid #e2e8f0;
        }
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        .detail-icon { font-size: 1.1rem; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
        }
        .status {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Industrial Property Scraper</h1>
            <p>Trouvez votre bâtiment industriel idéal sur Lyon et Bordeaux</p>
        </div>

        <div class="status">
            <strong>🚀 Statut :</strong> Application en cours de démarrage...
            <span class="spinner"></span>
            <br><small>Les installations npm sont en cours. L'application complète sera bientôt disponible.</small>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">Propriétés trouvées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">815k€</div>
                <div class="stat-label">Prix moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">750m²</div>
                <div class="stat-label">Surface moyenne</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89%</div>
                <div class="stat-label">Score moyen</div>
            </div>
        </div>

        <div class="properties">
            ${mockProperties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div>
                            <div class="property-title">${property.title}</div>
                            <div class="badges">
                                <span class="badge badge-primary">${property.source}</span>
                                <span class="badge badge-success">${property.matchScore}% match</span>
                            </div>
                        </div>
                        <div class="property-price">${property.price.toLocaleString('fr-FR')} €</div>
                    </div>

                    <div class="property-description">
                        ${property.description}
                    </div>

                    <div class="property-details">
                        <div class="detail-item">
                            <span class="detail-icon">🏙️</span>
                            <span>${property.city}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📐</span>
                            <span>${property.buildingArea}m² bâtiment</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🏞️</span>
                            <span>${property.landArea}m² terrain</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🎯</span>
                            <span>${property.matchScore}% correspondance</span>
                        </div>
                    </div>

                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="window.location.href='/property?id=${property.id}'">Voir détails</button>
                        <button class="btn btn-secondary" onclick="window.open('https://www.${property.source}.com', '_blank')">Voir annonce</button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
            <h3>🚀 Navigation</h3>
            <p style="margin-bottom: 20px;">Explorez les différentes fonctionnalités de l'application</p>
            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                <a href="/scraping" class="btn btn-primary">🔍 Gestion du scraping</a>
                <button class="btn btn-secondary" onclick="alert('Statistiques bientôt disponibles!')">📊 Statistiques</button>
                <button class="btn btn-secondary" onclick="alert('Configuration bientôt disponible!')">⚙️ Configuration</button>
            </div>
            <p style="margin-top: 20px; color: #64748b; font-size: 0.9rem;">
                Application de démonstration • Version complète en développement
            </p>
        </div>
    </div>

    <script>
        // Simulation de mise à jour en temps réel
        setInterval(() => {
            const spinner = document.querySelector('.spinner');
            if (spinner) {
                spinner.style.transform = 'rotate(' + (Date.now() / 10) + 'deg)';
            }
        }, 50);
    </script>
</body>
</html>
`;

// Fonction pour générer la page de détail d'une propriété
function generatePropertyDetailHTML(property) {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 ${property.title} - Industrial Property Scraper</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #f1f5f9;
            color: #475569;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 20px;
            transition: background 0.2s;
        }
        .back-btn:hover { background: #e2e8f0; }
        .property-header {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .property-title { font-size: 2rem; font-weight: bold; margin-bottom: 10px; }
        .property-price { font-size: 2.5rem; font-weight: bold; color: #059669; margin-bottom: 15px; }
        .badges { display: flex; gap: 10px; margin-bottom: 15px; }
        .badge {
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        .badge-primary { background: #dbeafe; color: #1d4ed8; }
        .badge-success { background: #dcfce7; color: #166534; }
        .property-description { color: #64748b; font-size: 1.1rem; }
        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .detail-card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .detail-card h3 {
            font-size: 1.25rem;
            margin-bottom: 15px;
            color: #1e293b;
        }
        .detail-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #f1f5f9;
        }
        .detail-item:last-child { border-bottom: none; }
        .detail-label { color: #64748b; }
        .detail-value { font-weight: 500; }
        .action-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-top: 20px;
            text-align: center;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary { background: #3b82f6; color: white; }
        .btn-primary:hover { background: #2563eb; }
        .btn-secondary { background: #f1f5f9; color: #475569; }
        .btn-secondary:hover { background: #e2e8f0; }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-btn">← Retour aux propriétés</a>

        <div class="property-header">
            <h1 class="property-title">${property.title}</h1>
            <div class="property-price">${property.price.toLocaleString('fr-FR')} €</div>
            <div class="badges">
                <span class="badge badge-primary">${property.source}</span>
                <span class="badge badge-success">${property.matchScore}% correspondance</span>
            </div>
            <p class="property-description">${property.description}</p>
        </div>

        <div class="details-grid">
            <div class="detail-card">
                <h3>🏢 Caractéristiques du bâtiment</h3>
                <div class="detail-item">
                    <span class="detail-label">Superficie</span>
                    <span class="detail-value">${property.buildingArea} m²</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Hauteur sous plafond</span>
                    <span class="detail-value">6,2 m</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Bureaux</span>
                    <span class="detail-value">120 m²</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Accès poids lourds</span>
                    <span class="detail-value">✅ Oui</span>
                </div>
            </div>

            <div class="detail-card">
                <h3>🏞️ Terrain et localisation</h3>
                <div class="detail-item">
                    <span class="detail-label">Superficie terrain</span>
                    <span class="detail-value">${property.landArea} m²</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Ville</span>
                    <span class="detail-value">${property.city}</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Distance autoroute</span>
                    <span class="detail-value">8 minutes</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Stockage extérieur</span>
                    <span class="detail-value">✅ Possible</span>
                </div>
            </div>
        </div>

        <div class="action-section">
            <h3>Actions disponibles</h3>
            <p style="margin: 15px 0; color: #64748b;">Consultez l'annonce originale ou sauvegardez cette propriété</p>
            <a href="https://www.${property.source}.com" target="_blank" class="btn btn-primary">
                🔗 Voir l'annonce originale
            </a>
            <button class="btn btn-secondary" onclick="alert('Propriété sauvegardée!')">
                💾 Sauvegarder
            </button>
        </div>
    </div>
</body>
</html>
  `;
}

// Fonction pour générer la page de scraping
function generateScrapingHTML() {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Scraping - Industrial Property Scraper</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        .container { max-width: 1000px; margin: 0 auto; padding: 20px; }
        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            background: #f1f5f9;
            color: #475569;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 20px;
            transition: background 0.2s;
        }
        .back-btn:hover { background: #e2e8f0; }
        .scraping-control {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin: 10px;
            font-size: 1.1rem;
        }
        .btn-success { background: #10b981; color: white; }
        .btn-success:hover { background: #059669; }
        .btn-secondary { background: #f1f5f9; color: #475569; }
        .btn-secondary:hover { background: #e2e8f0; }
        .progress-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        .progress-fill {
            height: 100%;
            background: #10b981;
            width: 0%;
            transition: width 0.3s ease;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .results-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .result-item {
            padding: 15px;
            border-left: 4px solid #10b981;
            background: #f0fdf4;
            margin: 10px 0;
            border-radius: 0 8px 8px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="/" class="back-btn">← Retour au tableau de bord</a>

        <div class="scraping-control">
            <h1>🔍 Gestion du scraping</h1>
            <p style="margin: 15px 0; color: #64748b;">
                Lancez le processus de scraping pour rechercher de nouvelles propriétés industrielles
            </p>
            <button id="startBtn" class="btn btn-success" onclick="startScraping()">
                🚀 Démarrer le scraping complet
            </button>
            <button class="btn btn-secondary" onclick="alert('Fonctionnalité bientôt disponible!')">
                🌐 Geolocaux uniquement
            </button>
            <button class="btn btn-secondary" onclick="alert('Fonctionnalité bientôt disponible!')">
                🏢 SeLoger uniquement
            </button>
        </div>

        <div id="progressSection" class="progress-section">
            <h3>Scraping en cours...</h3>
            <div id="currentStep">Initialisation...</div>
            <div class="progress-bar">
                <div id="progressFill" class="progress-fill"></div>
            </div>
            <div style="color: #64748b; font-size: 0.9rem;">
                <span class="spinner"></span>
                Veuillez patienter pendant que nous recherchons les propriétés...
            </div>
        </div>

        <div class="results-section">
            <h3>📊 Historique des scraping</h3>
            <div class="result-item">
                <strong>✅ Scraping réussi</strong> - Il y a 2 heures<br>
                <small>Geolocaux + SeLoger • 15 propriétés trouvées • 3 nouvelles ajoutées</small>
            </div>
            <div class="result-item">
                <strong>✅ Scraping réussi</strong> - Il y a 8 heures<br>
                <small>Scraping automatique • 8 propriétés trouvées • 1 nouvelle ajoutée</small>
            </div>
        </div>
    </div>

    <script>
        function startScraping() {
            const btn = document.getElementById('startBtn');
            const progressSection = document.getElementById('progressSection');
            const currentStep = document.getElementById('currentStep');
            const progressFill = document.getElementById('progressFill');

            btn.disabled = true;
            btn.textContent = '⏳ Scraping en cours...';
            progressSection.style.display = 'block';

            const steps = [
                'Initialisation du navigateur...',
                'Connexion à Geolocaux...',
                'Scraping des annonces Lyon...',
                'Scraping des annonces Bordeaux...',
                'Connexion à SeLoger...',
                'Traitement des données...',
                'Sauvegarde en base...',
                'Terminé !'
            ];

            let currentStepIndex = 0;

            const interval = setInterval(() => {
                if (currentStepIndex < steps.length) {
                    currentStep.textContent = steps[currentStepIndex];
                    progressFill.style.width = ((currentStepIndex + 1) / steps.length * 100) + '%';
                    currentStepIndex++;
                } else {
                    clearInterval(interval);
                    setTimeout(() => {
                        alert('🎉 Scraping terminé ! 5 nouvelles propriétés trouvées.');
                        btn.disabled = false;
                        btn.textContent = '🚀 Démarrer le scraping complet';
                        progressSection.style.display = 'none';
                        progressFill.style.width = '0%';
                    }, 1000);
                }
            }, 1500);
        }
    </script>
</body>
</html>
  `;
}

const server = http.createServer((req, res) => {
  const url = new URL(req.url, `http://${req.headers.host}`);

  if (url.pathname === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(demoHTML);
  } else if (url.pathname === '/api/properties') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ success: true, data: mockProperties }));
  } else if (url.pathname === '/property') {
    const id = url.searchParams.get('id');
    const property = mockProperties.find(p => p.id === id);

    if (property) {
      const detailHTML = generatePropertyDetailHTML(property);
      res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
      res.end(detailHTML);
    } else {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Propriété non trouvée');
    }
  } else if (url.pathname === '/scraping') {
    const scrapingHTML = generateScrapingHTML();
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(scrapingHTML);
  } else if (url.pathname === '/api/scraping/start') {
    // Simulation du scraping
    setTimeout(() => {
      res.writeHead(200, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({
        success: true,
        message: 'Scraping simulé terminé',
        propertiesFound: 5,
        propertiesAdded: 2
      }));
    }, 2000);
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Serveur de démonstration démarré sur http://localhost:${PORT}`);
  console.log(`📊 Interface disponible sur: http://localhost:${PORT}`);
  console.log(`🔧 Installation des dépendances en cours...`);
});
