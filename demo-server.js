// Serveur de démonstration simple pour Industrial Property Scraper
const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// Données de démonstration
const mockProperties = [
  {
    id: 'geolocaux-123456',
    title: 'Entrepôt industriel avec bureaux - Lyon Est',
    description: 'Bâtiment industriel de 850m² avec bureaux de 120m², accès poids lourds, quai de chargement.',
    price: 850000,
    city: 'Lyon',
    buildingArea: 850,
    landArea: 4200,
    source: 'geolocaux',
    matchScore: 92
  },
  {
    id: 'seloger-789012',
    title: 'Local d\'activité moderne - Bordeaux Nord',
    description: 'Local d\'activité de 650m² avec terrain de 3800m², proche A10, sols industriels.',
    price: 780000,
    city: 'Bordeaux',
    buildingArea: 650,
    landArea: 3800,
    source: 'seloger',
    matchScore: 87
  }
];

// Page HTML de démonstration
const demoHTML = `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 Industrial Property Scraper - Démo</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8fafc;
            color: #334155;
            line-height: 1.6;
        }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { 
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
            color: white;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 30px;
            border-radius: 12px;
        }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number { font-size: 2rem; font-weight: bold; color: #3b82f6; }
        .stat-label { color: #64748b; margin-top: 5px; }
        .properties { 
            display: grid;
            gap: 20px;
        }
        .property-card {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .property-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        .property-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }
        .property-title { 
            font-size: 1.25rem; 
            font-weight: 600; 
            color: #1e293b;
            margin-bottom: 5px;
        }
        .property-price { 
            font-size: 1.5rem; 
            font-weight: bold; 
            color: #059669;
        }
        .badges {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
        }
        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .badge-primary { background: #dbeafe; color: #1d4ed8; }
        .badge-success { background: #dcfce7; color: #166534; }
        .property-description {
            color: #64748b;
            margin-bottom: 15px;
        }
        .property-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            padding-top: 15px;
            border-top: 1px solid #e2e8f0;
        }
        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        .detail-icon { font-size: 1.1rem; }
        .loading {
            text-align: center;
            padding: 40px;
            color: #64748b;
        }
        .spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e2e8f0;
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
        }
        .btn-secondary:hover {
            background: #e2e8f0;
        }
        .status {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #10b981;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 Industrial Property Scraper</h1>
            <p>Trouvez votre bâtiment industriel idéal sur Lyon et Bordeaux</p>
        </div>

        <div class="status">
            <strong>🚀 Statut :</strong> Application en cours de démarrage... 
            <span class="spinner"></span>
            <br><small>Les installations npm sont en cours. L'application complète sera bientôt disponible.</small>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">2</div>
                <div class="stat-label">Propriétés trouvées</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">815k€</div>
                <div class="stat-label">Prix moyen</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">750m²</div>
                <div class="stat-label">Surface moyenne</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89%</div>
                <div class="stat-label">Score moyen</div>
            </div>
        </div>

        <div class="properties">
            ${mockProperties.map(property => `
                <div class="property-card">
                    <div class="property-header">
                        <div>
                            <div class="property-title">${property.title}</div>
                            <div class="badges">
                                <span class="badge badge-primary">${property.source}</span>
                                <span class="badge badge-success">${property.matchScore}% match</span>
                            </div>
                        </div>
                        <div class="property-price">${property.price.toLocaleString('fr-FR')} €</div>
                    </div>
                    
                    <div class="property-description">
                        ${property.description}
                    </div>
                    
                    <div class="property-details">
                        <div class="detail-item">
                            <span class="detail-icon">🏙️</span>
                            <span>${property.city}</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">📐</span>
                            <span>${property.buildingArea}m² bâtiment</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🏞️</span>
                            <span>${property.landArea}m² terrain</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-icon">🎯</span>
                            <span>${property.matchScore}% correspondance</span>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="btn btn-primary">Voir détails</button>
                        <button class="btn btn-secondary">Voir annonce</button>
                    </div>
                </div>
            `).join('')}
        </div>

        <div style="text-align: center; margin-top: 40px; padding: 20px; background: white; border-radius: 8px;">
            <h3>🔄 Installation en cours</h3>
            <p>L'application complète avec React et le système de scraping sera bientôt disponible.</p>
            <p><strong>Frontend:</strong> http://localhost:3000 (en cours d'installation)</p>
            <p><strong>Backend:</strong> http://localhost:3001 (en cours d'installation)</p>
        </div>
    </div>

    <script>
        // Simulation de mise à jour en temps réel
        setInterval(() => {
            const spinner = document.querySelector('.spinner');
            if (spinner) {
                spinner.style.transform = 'rotate(' + (Date.now() / 10) + 'deg)';
            }
        }, 50);
    </script>
</body>
</html>
`;

const server = http.createServer((req, res) => {
  if (req.url === '/') {
    res.writeHead(200, { 'Content-Type': 'text/html; charset=utf-8' });
    res.end(demoHTML);
  } else if (req.url === '/api/properties') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(JSON.stringify({ success: true, data: mockProperties }));
  } else {
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page non trouvée');
  }
});

server.listen(PORT, () => {
  console.log(`🚀 Serveur de démonstration démarré sur http://localhost:${PORT}`);
  console.log(`📊 Interface disponible sur: http://localhost:${PORT}`);
  console.log(`🔧 Installation des dépendances en cours...`);
});
