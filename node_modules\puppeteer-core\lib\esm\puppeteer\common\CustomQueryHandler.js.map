{"version": 3, "file": "CustomQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/CustomQueryHandler.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAGH,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,mBAAmB,EAAE,iBAAiB,EAAC,MAAM,qBAAqB,CAAC;AAE3E,OAAO,EACL,YAAY,GAGb,MAAM,mBAAmB,CAAC;AAC3B,OAAO,EAAC,cAAc,EAAC,MAAM,qBAAqB,CAAC;AAgBnD;;;;;;;;;;;GAWG;AACH,MAAM,OAAO,0BAA0B;IACrC,SAAS,GAAG,IAAI,GAAG,EAGhB,CAAC;IAEJ,GAAG,CAAC,IAAY;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,QAAQ,CAAC,IAAY,EAAE,OAA2B;QAChD,MAAM,CACJ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EACzB,0CAA0C,IAAI,EAAE,CACjD,CAAC;QACF,MAAM,CACJ,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,sDAAsD,CACvD,CAAC;QACF,MAAM,CACJ,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EACpC,gDAAgD,CACjD,CAAC;QAEF,MAAM,OAAO,GAAG,KAAM,SAAQ,YAAY;YACxC,MAAM,CAAU,gBAAgB,GAAqB,mBAAmB,CACtE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC7B,CAAC;YACF,MAAM,CAAU,aAAa,GAAkB,mBAAmB,CAChE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACnC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC7B,CAAC;SACH,CAAC;QACF,MAAM,cAAc,GAAG,mBAAmB,CACxC,CAAC,aAA4B,EAAE,EAAE;YAC/B,aAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBAC/D,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;gBACjC,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,EACD;YACE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;YACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;SACtB,CACF,CAAC,QAAQ,EAAE,CAAC;QAEb,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QACpD,cAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,UAAU,CAAC,IAAY;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;QACD,cAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACF;AAED;;GAEG;AACH,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,0BAA0B,EAAE,CAAC"}