/**
 * Character class utilities for XML NS 1.0 edition 3.
 *
 * <AUTHOR>
 * @license MIT
 * @copyright <PERSON><PERSON><PERSON>
 */
export declare const NC_NAME_START_CHAR = "A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\uD800\uDC00-\uDB7F\uDFFF";
export declare const NC_NAME_CHAR: string;
export declare const NC_NAME_START_CHAR_RE: RegExp;
export declare const NC_NAME_CHAR_RE: RegExp;
export declare const NC_NAME_RE: RegExp;
/**
 * Determines whether a codepoint matches [[NC_NAME_START_CHAR]].
 *
 * @param c The code point.
 *
 * @returns ``true`` if the codepoint matches.
 */
export declare function isNCNameStartChar(c: number): boolean;
/**
 * Determines whether a codepoint matches [[NC_NAME_CHAR]].
 *
 * @param c The code point.
 *
 * @returns ``true`` if the codepoint matches.
 */
export declare function isNCNameChar(c: number): boolean;
